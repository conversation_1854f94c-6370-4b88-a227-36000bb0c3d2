/* Hero Slider Styles */

.hero-slider {
  width: 100%;
  height: 100%;
  padding: 30px 0;
}

.hero-slide {
  width: 75%;
  height: auto;
  transition: transform 0.5s ease, opacity 0.5s ease;
  opacity: 0.7;
  transform: scale(0.9);
}

.swiper-slide-active.hero-slide {
  opacity: 1;
  transform: scale(1);
}

.swiper-slide-prev.hero-slide,
.swiper-slide-next.hero-slide {
  opacity: 0.8;
  transform: scale(0.95);
}

/* Custom pagination */
.hero-slider .swiper-pagination {
  bottom: 0px;
}

.hero-slider .swiper-pagination-bullet {
  width: 10px;
  height: 10px;
  background: rgba(255, 255, 255, 0.3);
  opacity: 1;
  transition: all 0.3s ease;
}

.hero-slider .swiper-pagination-bullet-active {
  background: linear-gradient(to right, #1E40AF, #3B82F6, #60A5FA);
  transform: scale(1.2);
}

/* Custom navigation */
.hero-slider .swiper-button-next,
.hero-slider .swiper-button-prev {
  color: white;
  background: linear-gradient(135deg, rgba(30, 64, 175, 0.7), rgba(59, 130, 246, 0.7));
  width: 40px;
  height: 40px;
  border-radius: 50%;
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
  transition: all 0.3s ease;
  border: 1px solid rgba(96, 165, 250, 0.3);
}

.hero-slider .swiper-button-next:hover,
.hero-slider .swiper-button-prev:hover {
  background: linear-gradient(135deg, rgba(30, 64, 175, 0.9), rgba(59, 130, 246, 0.9));
  transform: scale(1.1);
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
}

.hero-slider .swiper-button-next:after,
.hero-slider .swiper-button-prev:after {
  font-size: 18px;
  font-weight: bold;
}

/* Coverflow effect enhancements */
.swiper-coverflow .swiper-wrapper {
  perspective: 1200px !important;
}

/* Animation for slide content */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-slide h2,
.hero-slide p,
.hero-slide button,
.hero-slide .icon-container {
  animation: fadeInUp 0.8s ease forwards;
}

.hero-slide h2 {
  animation-delay: 0.2s;
}

.hero-slide p {
  animation-delay: 0.4s;
}

.hero-slide button {
  animation-delay: 0.6s;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hero-slider {
    padding: 10px 0;
    height: 100%;
  }

  .hero-slide {
    width: 95%;
    height: 100%;
  }

  .hero-slider .swiper-button-next,
  .hero-slider .swiper-button-prev {
    width: 30px;
    height: 30px;
    display: none; /* Hide navigation arrows on mobile */
  }

  .hero-slider .swiper-button-next:after,
  .hero-slider .swiper-button-prev:after {
    font-size: 14px;
  }

  .hero-slider .swiper-pagination-bullet {
    width: 8px;
    height: 8px;
  }

  /* Adjust content layout for mobile */
  .hero-slide h2 {
    font-size: 1.5rem;
  }

  .hero-slide p {
    font-size: 0.875rem;
    margin-bottom: 1rem;
  }

  .hero-slide button {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
}
