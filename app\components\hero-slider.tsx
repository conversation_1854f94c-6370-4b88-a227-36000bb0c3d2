'use client'

import { useEffect, useState } from 'react'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Autoplay, EffectCoverflow, Navigation, Pagination } from 'swiper/modules'
import { Button } from '@/components/ui/button'
import { ChevronRight, TrendingUp, Shield, Award, Globe } from 'lucide-react'
import Image from 'next/image'

// Import Swiper styles
import 'swiper/css'
import 'swiper/css/effect-coverflow'
import 'swiper/css/navigation'
import 'swiper/css/pagination'

// Define the slide data
const sliderItems = [
  {
    id: 1,
    title: "Advanced Trading Platform",
    description: "Access cutting-edge tools and analytics to maximize your trading potential",
    image: "/trading-platform.jpg",
    icon: TrendingUp,
    ctaText: "Explore Platform",
    ctaLink: "/dashboard",
    gradient: "from-[#1E40AF]/30 via-[#3B82F6]/20 to-[#60A5FA]/10"
  },
  {
    id: 2,
    title: "Instant Account Funding",
    description: "Pass our challenge and receive up to $2.5M in trading capital within 24 hours",
    image: "/funding.jpg",
    icon: Shield,
    ctaText: "Get Funded",
    ctaLink: "/signup",
    gradient: "from-[#1E3A8A]/30 via-[#2563EB]/20 to-[#3B82F6]/10"
  },
  {
    id: 3,
    title: "Global Trading Community",
    description: "Join thousands of successful traders from over 150 countries worldwide",
    image: "/community.jpg",
    icon: Globe,
    ctaText: "Join Community",
    ctaLink: "/#community",
    gradient: "from-[#1E3A8A]/30 via-[#1E40AF]/20 to-[#3B82F6]/10"
  },
  {
    id: 4,
    title: "Award-Winning Support",
    description: "Our 24/7 support team ensures you have everything you need to succeed",
    image: "/support.jpg",
    icon: Award,
    ctaText: "Contact Support",
    ctaLink: "/support",
    gradient: "from-[#0F172A]/50 via-[#1E3A8A]/30 to-[#3B82F6]/20"
  }
]

export default function HeroSlider() {
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    setIsLoaded(true)
  }, [])

  const handleNavigation = (path: string) => {
    // For hash links on the landing page
    if (path.startsWith('#')) {
      const element = document.querySelector(path)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
        return
      }
    }

    // Ensure the path starts with a forward slash
    const normalizedPath = path.startsWith('/') ? path : `/${path}`
    window.location.href = normalizedPath
  }

  return (
    <div className="w-full h-full relative">
      {isLoaded && (
        <Swiper
          modules={[Autoplay, EffectCoverflow, Navigation, Pagination]}
          effect="coverflow"
          grabCursor={true}
          centeredSlides={true}
          slidesPerView="auto"
          coverflowEffect={{
            rotate: 0,
            stretch: 0,
            depth: 80,
            modifier: 1.5,
            slideShadows: false,
          }}
          autoplay={{
            delay: 5000,
            disableOnInteraction: false,
            pauseOnMouseEnter: true,
          }}
          pagination={{
            clickable: true,
            dynamicBullets: true,
          }}
          navigation={{
            enabled: true,
            hideOnClick: false,
          }}
          loop={true}
          speed={800}
          breakpoints={{
            // when window width is >= 320px
            320: {
              slidesPerView: 1,
              spaceBetween: 10,
              coverflowEffect: {
                rotate: 0,
                stretch: 0,
                depth: 50,
                modifier: 1.5,
              },
            },
            // when window width is >= 640px
            640: {
              slidesPerView: 'auto',
              spaceBetween: 15,
              coverflowEffect: {
                rotate: 0,
                stretch: 0,
                depth: 70,
                modifier: 1.5,
              },
            },
            // when window width is >= 1024px
            1024: {
              slidesPerView: 'auto',
              spaceBetween: 10,
              coverflowEffect: {
                rotate: 0,
                stretch: 0,
                depth: 60,
                modifier: 1.2,
              },
            },
          }}
          className="hero-slider w-full"
        >
          {sliderItems.map((item) => {
            const Icon = item.icon
            return (
              <SwiperSlide key={item.id} className="hero-slide">
                <div className={`relative w-full h-full flex flex-col md:flex-row items-center justify-between p-3 sm:p-6 md:p-10 rounded-xl overflow-hidden bg-gradient-to-br ${item.gradient} backdrop-blur-sm border border-white/10`}>
                  {/* Background gradient */}
                  <div className="absolute inset-0 bg-[#0F172A]/80 z-0"></div>

                  {/* Content */}
                  <div className="relative z-10 w-full md:w-1/2 text-left mb-2 md:mb-0">
                    <div className="flex items-center mb-2 md:mb-4">
                      <div className="w-8 h-8 md:w-12 md:h-12 rounded-full bg-gradient-to-r from-[#1E40AF] via-[#3B82F6] to-[#60A5FA] flex items-center justify-center mr-2 md:mr-4">
                        <Icon className="w-4 h-4 md:w-6 md:h-6 text-white" />
                      </div>
                      <h2 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-white">
                        {item.title}
                      </h2>
                    </div>

                    <p className="text-sm sm:text-base md:text-lg text-gray-300 mb-3 md:mb-8">
                      {item.description}
                    </p>

                    <Button
                      type="button"
                      onClick={() => handleNavigation(item.ctaLink)}
                      className="bg-gradient-to-r from-[#1E40AF] via-[#3B82F6] to-[#60A5FA] text-white px-4 py-2 md:px-6 md:py-3 rounded-lg text-sm md:text-base font-semibold transition-all duration-300 hover:shadow-lg hover:shadow-[#3B82F6]/20"
                    >
                      {item.ctaText}
                      <ChevronRight className="ml-1 md:ml-2 w-4 h-4 md:w-5 md:h-5" />
                    </Button>
                  </div>

                  {/* Image */}
                  <div className="relative z-10 w-full md:w-1/2 h-[280px] sm:h-[280px] md:h-[300px] rounded-lg overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-r from-[#0F172A]/80 via-transparent to-transparent z-10 md:hidden"></div>
                    <Image
                      src={item.image}
                      alt={item.title}
                      fill
                      className="object-cover"
                      sizes="(max-width: 768px) 100vw, 50vw"
                      priority
                    />
                  </div>
                </div>
              </SwiperSlide>
            )
          })}
        </Swiper>
      )}
    </div>
  )
}
