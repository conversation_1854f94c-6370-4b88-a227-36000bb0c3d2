"use client"

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { X } from 'lucide-react'
import { Button } from "@/components/ui/button"
import Image from 'next/image'
import {
  Dialog,
  DialogContent,
  DialogClose,
} from "@/components/ui/dialog"

export function SaleOfferModal() {
  const [isOpen, setIsOpen] = useState(false)
  const [timeLeft, setTimeLeft] = useState({
    days: 10,
    hours: 0,
    minutes: 0,
    seconds: 0
  })

  useEffect(() => {
    // Show the modal after a delay
    const timer = setTimeout(() => setIsOpen(true), 3000)

    // Countdown timer logic
    const interval = setInterval(() => {
      setTimeLeft(prev => {
        if (prev.seconds > 0) return { ...prev, seconds: prev.seconds - 1 }
        if (prev.minutes > 0) return { ...prev, minutes: prev.minutes - 1, seconds: 59 }
        if (prev.hours > 0) return { ...prev, hours: prev.hours - 1, minutes: 59, seconds: 59 }
        if (prev.days > 0) return { days: prev.days - 1, hours: 23, minutes: 59, seconds: 59 }
        return prev
      })
    }, 1000)

    return () => {
      clearTimeout(timer)
      clearInterval(interval)
    }
  }, [])

  const accounts = [
    { size: '$1K', price: '$6', original: '$7.50' },
    { size: '$3K', price: '$12', original: '$15.00' },
    { size: '$5K', price: '$21', original: '$26.25' },
    { size: '$10K', price: '$33', original: '$41.25' },
    { size: '$25K', price: '$72', original: '$90.00' },
    { size: '$50K', price: '$42', original: '$140.00' },
    { size: '$100K', price: '$65', original: '$216.67' },
    { size: '$200K', price: '$128', original: '$426.67' },
    { size: '$500K', price: '$225', original: '$750.00' },
  ];

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="bg-black/80 backdrop-blur-sm text-white border-none w-full h-full max-w-none sm:rounded-none p-0 flex items-center justify-center">
          <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, ease: [0.22, 1, 0.36, 1] }}
          className="relative w-full max-w-sm md:max-w-5xl bg-black p-3 md:p-6 rounded-2xl overflow-y-auto md:overflow-hidden mt-4 sm:mt-0 max-h-[90vh] md:max-h-none"
        >
          {/* Decorative Wavy Lines */}
          <svg
            className="absolute top-0 left-0 -translate-x-1/4 -translate-y-1/4 pointer-events-none"
            width="800"
            height="600"
            viewBox="0 0 800 600"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <motion.path
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              transition={{ duration: 2, ease: "easeInOut" }}
              d="M-50,300 C150,100 350,500 850,300"
              stroke="#e4b85c"
              strokeWidth="2"
              opacity="0.3"
            />
          </svg>
          <svg
            className="absolute bottom-0 right-0 translate-x-1/4 translate-y-1/4 pointer-events-none"
            width="800"
            height="600"
            viewBox="0 0 800 600"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <motion.path
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              transition={{ duration: 2, ease: "easeInOut", delay: 0.5 }}
              d="M850,300 C650,500 450,100 -50,300"
              stroke="#e4b85c"
              strokeWidth="2"
              opacity="0.3"
            />
          </svg>

          <DialogClose className="absolute top-6 right-6 text-gray-400 hover:text-white z-20">
            <X className="w-6 h-6" />
          </DialogClose>

          <div className="relative z-10 flex flex-col items-center text-center">
            <Image src="/logo.svg" alt="Funded Horizon Logo" width={180} height={45} className="mb-4 h-auto" />
            <Image src="/logo.svg" alt="Funded Horizon Logo" width={120} height={30} className="mb-2 md:mb-4 h-auto" />
            
            <h1 className="text-3xl md:text-2xl font-bold text-center text-white mb-2">
              <span className="text-xl md:text-2xl">Exclusive Trading Challenge</span>
            </h1>
            <p className="text-gray-300 text-center text-xs md:text-sm mb-3 md:mb-4">
              Unlock Premium Accounts at <span className="text-[#e4b85c]">Unbeatable Prices</span>
            </p>

            {/* Timer Section */}
            <div className="flex justify-center items-center gap-2 md:gap-2 mb-4 md:mb-3">
                <span className="text-gray-300 text-xs md:text-xs">Offer Ends In:</span>
                <div className="flex items-center gap-2 md:gap-1.5">
                {Object.entries(timeLeft).map(([label, value]) => (
                    <div key={label} className="flex flex-col items-center">
                    <div className="w-8 h-8 md:w-9 md:h-9 bg-gray-800/50 rounded-md flex items-center justify-center border border-gray-700">
                        <span className="text-sm md:text-sm font-bold text-[#e4b85c]">
                        {String(value).padStart(2, '0')}
                        </span>
                    </div>
                    <span className="text-[8px] md:text-[9px] text-gray-400 mt-1 capitalize">{label}</span>
                  </div>
                ))}
                </div>
            </div>

            {/* Account Grid */}
            <div className="grid grid-cols-3 gap-2 md:gap-1.5 w-full md:grid-cols-9 mb-3 md:mb-2">
                {accounts.map((account) => (
                <motion.div
                    key={account.size}
                    whileHover={{ y: -5, borderColor: '#e4b85c' }}
                    className="bg-gray-900/50 p-2 md:p-1.5 rounded-md border border-gray-700 transition-colors cursor-pointer"
                    onClick={() => {
                        setIsOpen(false);
                        window.location.href = '/signup';
                    }}
                >
                    <h4 className="font-bold text-xs md:text-[11px] text-white">{account.size}</h4>
                    <p className="text-[#e4b85c] font-bold text-sm md:text-sm">{account.price}</p>
                    <p className="text-gray-500 text-[8px] md:text-[9px] line-through">{account.original}</p>
              </motion.div>
            ))}
          </div>

            {/* Giveaway Info */}
            <motion.div
              className="border border-dashed border-[#e4b85c]/50 rounded-lg p-2 md:p-1.5 my-3 md:my-2 w-full max-w-2xl"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.6 }}
            >
              <p className="text-xs md:text-[11px] font-bold text-white text-center">
                Register for the giveaway to win a <span className="text-[#e4b85c]">$100K Account</span> by purchasing an account of $50K or more!
              </p>
            </motion.div>

          {/* CTA Button */}
            <Button 
                className="w-full max-w-sm mx-auto bg-[#e4b85c] text-black font-bold text-sm md:text-xs py-2 md:py-1.5 rounded-lg hover:bg-yellow-500 transition-all duration-300 shadow-[0_0_20px_rgba(228,184,92,0.4)]"
              onClick={() => {
                setIsOpen(false);
                window.location.href = '/signup';
              }}
            >
                Start Your Trading Journey
            </Button>
            
            <Button variant="link" onClick={() => setIsOpen(false)} className="text-gray-400 mt-2 md:mt-1.5 hover:text-white text-xs md:text-xs">
                Decline Offer
            </Button>
          </div>
          </motion.div>
      </DialogContent>
    </Dialog>
  )
}
