import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { validateRequest, getSecurityHeaders, RateLimiter, logSecurityEvent } from '@/lib/security'

export function middleware(request: NextRequest) {
  // Get the pathname of the request
  const path = request.nextUrl.pathname

  // Skip middleware for static files and API routes to improve performance
  if (path.startsWith('/_next/') || path.startsWith('/api/') || path.includes('.')) {
    return NextResponse.next()
  }

  // Security: Validate request (with more lenient validation for production)
  const requestValidation = validateRequest(request)
  if (!requestValidation.valid) {
    // Only block obviously malicious requests in production
    if (process.env.NODE_ENV === 'production') {
      if (requestValidation.reason && (requestValidation.reason.includes('sqlmap') || requestValidation.reason.includes('nikto'))) {
        logSecurityEvent('Request blocked', { reason: requestValidation.reason, path }, 'high')
        return new NextResponse('Forbidden', { status: 403 })
      }
    } else {
      // In development, be more lenient and just log the issue
      console.warn('Request validation failed in development:', requestValidation.reason || 'Unknown reason', path)
      // Don't block the request in development mode
    }
  }

  // Security: Rate limiting (more lenient in production)
  const clientIP = request.ip || request.headers.get('x-forwarded-for') || 'unknown'
  const rateLimit = RateLimiter.checkRateLimit(clientIP)
  
  if (!rateLimit.allowed) {
    logSecurityEvent('Rate limit exceeded', { clientIP, path }, 'medium')
    // In development, be more lenient with rate limiting
    if (process.env.NODE_ENV === 'production') {
      return new NextResponse('Too Many Requests', { 
        status: 429,
        headers: {
          'Retry-After': Math.ceil((rateLimit.resetTime - Date.now()) / 1000).toString()
        }
      })
    } else {
      console.warn('Rate limit exceeded in development:', { clientIP, path })
      // Don't block in development
    }
  }

  // Define public paths that don't require authentication
  const isPublicPath = path === '/login' || path === '/signup' || path === '/forgot-password' || path === '/reset-password' || path === '/verify-email' || path === '/sigin'

  // Get tokens from cookies
  const accessToken = request.cookies.get('access_token')?.value || ''
  const adminToken = request.cookies.get('admin-token')?.value || ''
  const loginTime = request.cookies.get('login_time')?.value || ''

  // Check if token is expired (24 hours)
  const isTokenExpired = (loginTimeStr: string): boolean => {
    if (!loginTimeStr) return true;
    const loginTimestamp = parseInt(loginTimeStr);
    const currentTime = Date.now();
    const twentyFourHours = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
    return (currentTime - loginTimestamp) > twentyFourHours;
  };

  // Handle dashboard authentication
  if (path.startsWith('/dashboard')) {
    // Check if user has valid access token
    if (!accessToken || isTokenExpired(loginTime)) {
      logSecurityEvent('Unauthorized dashboard access attempt', { path, clientIP }, 'medium')
      return NextResponse.redirect(new URL('/sigin', request.url))
    }
  }

  // Handle admin dashboard authentication
  if (path.startsWith('/dashboard/admin')) {
    // Check if user has valid admin token
    if (!adminToken) {
      logSecurityEvent('Unauthorized admin access attempt', { path, clientIP }, 'medium')
      return NextResponse.redirect(new URL('/login', request.url))
    }
  }

  // If we're on a public path and have a valid token, redirect appropriately
  if (isPublicPath) {
    if (accessToken && !isTokenExpired(loginTime)) {
      return NextResponse.redirect(new URL('/dashboard', request.url))
    }
    if (adminToken) {
      return NextResponse.redirect(new URL('/dashboard/admin', request.url))
    }
  }

  // Create response
  const response = NextResponse.next()

  // Add basic security headers to all responses (simplified for production)
  const securityHeaders = {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
  }
  
  Object.entries(securityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value)
  })

  // Add rate limit headers
  response.headers.set('X-RateLimit-Limit', '100')
  response.headers.set('X-RateLimit-Remaining', rateLimit.remainingAttempts.toString())
  response.headers.set('X-RateLimit-Reset', rateLimit.resetTime.toString())

  return response
}

// Configure the paths that middleware will run on (exclude API routes)
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api|_next/static|_next/image|favicon.ico|public).*)',
  ],
} 