import { Receipt } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { OrderDetails } from "@/types/order"
import { useState, useEffect } from "react"

interface PaymentProofModalProps {
  order: OrderDetails | null
  onClose: () => void
}

export function PaymentProofModal({ order, onClose }: PaymentProofModalProps) {
  const [imageError, setImageError] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  const imageUrl = order?.image?.image_url || order?.paymentProof
  
  return (
    <Dialog open={!!order} onOpenChange={() => onClose()}>
      <DialogContent className={`${isMobile ? 'max-w-[90vw] h-[70vh]' : 'max-w-xl'} bg-gray-900 border border-orange-800 shadow-2xl rounded-xl overflow-hidden`}>
        <DialogHeader className="mb-2">
          <DialogTitle className="text-lg font-bold text-white flex items-center gap-2">
            <div className="p-1.5 bg-orange-900 rounded-lg">
              <Receipt className="w-5 h-5 text-orange-300" />
            </div>
            Payment Proof
          </DialogTitle>
          <DialogDescription className="text-gray-400 text-sm mt-2">
            Payment proof for Order ID: {order?.order_id || order?.id}
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-auto p-4">
          {imageUrl && !imageError ? (
            <div className="relative w-full h-full flex items-center justify-center">
              <img 
                src={imageUrl}
                alt="Payment Proof" 
                className="object-contain max-h-[40vh] w-auto rounded-lg border border-gray-800"
                onError={() => setImageError(true)}
                style={{
                  maxWidth: isMobile ? '85%' : '90%',
                  margin: '0 auto'
                }}
              />
            </div>
          ) : (
            <div className="text-center p-8 bg-gray-800/50 rounded-lg border border-gray-700">
              <p className="text-gray-400">
                {imageError ? "Failed to load payment proof image" : "No payment proof available"}
              </p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
} 