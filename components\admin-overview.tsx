import { motion } from "framer-motion"
import { Card } from "@/components/ui/card"
import { ShoppingCart, CheckCircle, Award, Star, Zap } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { useState, useEffect } from "react"
import { getApiUrl, getEndpointPath } from "../app/config/api"

interface AdminOverviewProps {
  onSelectSection: (section: "orders" | "completedOrders" | "failedOrders" | "passOrders" | "stageTwoOrders" | "liveOrders" | "runningOrders" | "certificates" | "analytics" | "reports") => void
  selectedSection: "orders" | "completedOrders" | "failedOrders" | "passOrders" | "stageTwoOrders" | "liveOrders" | "runningOrders" | "certificates" | "analytics" | "reports" | null
}

export function AdminOverview({ onSelectSection, selectedSection }: AdminOverviewProps) {
  const [stats, setStats] = useState({ orders: 0, completed: 0, passed: 0, live: 0, running: 0, stageTwo: 0 });

  useEffect(() => {
    fetch(getApiUrl('order/stats'))
      .then((response) => response.json())
      .then((data) => setStats({ 
        orders: data.total_orders, 
        completed: data.completed_orders,
        passed: data.passed_orders || 0,
        live: data.live_orders || 0,
        running: data.running_orders || 0,
        stageTwo: data.stage_two_orders || 0
      }))
      .catch((error) => console.error("Error fetching order stats:", error));
  }, []);

  // Define card colors for luxury look
  const cardStyles = [
    { bg: "from-orange-900/40 to-orange-800/30", ring: "ring-orange-500/20", icon: "text-orange-400" },
    { bg: "from-teal-900/40 to-teal-800/30", ring: "ring-teal-500/20", icon: "text-teal-400" },
    { bg: "from-green-900/40 to-green-800/30", ring: "ring-green-500/20", icon: "text-green-400" },
    { bg: "from-yellow-900/40 to-yellow-800/30", ring: "ring-yellow-500/20", icon: "text-yellow-400" },
    { bg: "from-purple-900/40 to-purple-800/30", ring: "ring-purple-500/20", icon: "text-purple-400" },
    { bg: "from-indigo-900/40 to-indigo-800/30", ring: "ring-indigo-500/20", icon: "text-indigo-400" },
  ];

  const statItems = [
    { title: "Orders", icon: <ShoppingCart className="h-3 w-3" />, value: stats.orders },
    { title: "Completed", icon: <CheckCircle className="h-3 w-3" />, value: stats.completed },
    { title: "Passed", icon: <Award className="h-3 w-3" />, value: stats.passed },
    { title: "Stage Two", icon: <Star className="h-3 w-3" />, value: stats.stageTwo },
    { title: "Live", icon: <Zap className="h-3 w-3" />, value: stats.live },
    { title: "Running", icon: <CheckCircle className="h-3 w-3" />, value: stats.running }
  ];

  return (
    <div className="space-y-6">
      <motion.div
        className="grid gap-3 grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        {statItems.map((item, index) => (
          <motion.div
            key={item.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            whileHover={{ scale: 1.03, y: -2 }}
            whileTap={{ scale: 0.98 }}
          >
            <Card className={`
              flex flex-col items-center p-2 
              bg-gradient-to-br ${cardStyles[index].bg} 
              backdrop-blur-md shadow-xl 
              rounded-xl border-0
              ring-1 ${cardStyles[index].ring}
              transition-all duration-300
            `}>
              <div className="flex items-center mb-1 bg-black/20 p-1 rounded-full">
                <div className={`p-1 rounded-full ${cardStyles[index].icon}`}>
                  {item.icon}
                </div>
                <h3 className="ml-1 text-[10px] font-medium text-white/90">{item.title}</h3>
              </div>
              <div className="text-sm font-bold text-white drop-shadow-md">{item.value}</div>
            </Card>
          </motion.div>
        ))}
      </motion.div>

      <motion.div
        className="flex flex-wrap gap-2"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        {["orders", "completedOrders", "failedOrders", "passOrders", "stageTwoOrders", "liveOrders", "runningOrders", "certificates"].map((section, index) => (
          <motion.div 
            key={section} 
            whileHover={{ scale: 1.05, y: -2 }} 
            whileTap={{ scale: 0.95 }}
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.05 }}
          >
            <Button
              onClick={() => onSelectSection(section as any)}
              variant={selectedSection === section ? "default" : "outline"}
              className={`
                group flex items-center gap-1 px-3 py-1.5 rounded-lg font-medium text-[10px]
                ${selectedSection === section 
                  ? 'bg-gradient-to-r from-blue-600/90 to-purple-600/90 text-white shadow-lg backdrop-blur-sm ring-1 ring-white/10'
                  : 'bg-gradient-to-r from-slate-800/80 to-slate-900/80 hover:from-slate-700/80 border-slate-700/50 backdrop-blur-sm'
                }
                transition-all duration-300 hover:shadow-lg
              `}
            >
              <span className={`
                ${selectedSection === section 
                  ? 'text-white'
                  : 'text-gray-300 group-hover:text-white'
                }
              `}>
                {section === "passOrders" ? "Passed Orders" :
                 section === "stageTwoOrders" ? "Stage Two Orders" :
                 section === "liveOrders" ? "Live Orders" :
                 section === "certificates" ? "Certificates" :
                 section.replace(/([A-Z])/g, ' $1').trim()}
              </span>
            </Button>
          </motion.div>
        ))}
      </motion.div>
    </div>
  )
}
