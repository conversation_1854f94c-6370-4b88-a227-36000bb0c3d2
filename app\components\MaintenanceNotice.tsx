import React from 'react';

const MaintenanceNotice: React.FC = () => {
  return (
    <div className={`${typeof window !== 'undefined' && /iPhone|iPad|iPod/.test(navigator.userAgent) ? 'min-h-screen-ios' : 'min-h-screen'} flex flex-col justify-center items-center p-8 text-center relative overflow-hidden`}>
      {/* Animated Darker Gradient Background */}
      <div className="absolute inset-0 z-0 animate-gradient bg-gradient-to-br from-blue-950 via-blue-900 to-blue-800 opacity-95" style={{backgroundSize: '200% 200%'}} />
      <div className="absolute bottom-0 right-0 w-1/2 h-1/3 bg-gradient-to-tr from-orange-600 via-orange-500 to-transparent opacity-60 rounded-tl-full blur-2xl z-0" />
      <div className="relative z-10">
        <svg width="80" height="80" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="1.5" className="mb-4 text-orange-400">
          <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v2m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <h1 className="text-3xl font-bold mb-2 text-blue-100 drop-shadow">Website Under Maintenance</h1>
        <p className="text-lg font-bold max-w-md mb-2 text-orange-200 drop-shadow">
          We are currently performing scheduled maintenance.<br />
          <span className="text-orange-400">We will be back online and running smoothly very soon!</span>
        </p>
        <p className="text-base max-w-md text-blue-100 drop-shadow">
          Thank you for your patience and understanding.<br />
          Please check back again shortly.
        </p>
      </div>
      <style jsx>{`
        .animate-gradient {
          animation: gradientBG 6s ease-in-out infinite;
        }
        @keyframes gradientBG {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
      `}</style>
    </div>
  );
};

export default MaintenanceNotice; 