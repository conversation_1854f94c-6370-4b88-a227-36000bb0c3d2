@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Professional dark dashboard theme */
    --background: 222 47% 11%;
    --foreground: 210 40% 98%;

    --card: 217 33% 17%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;

    --primary: 221 83% 53%;
    --primary-foreground: 210 40% 98%;

    --secondary: 217 33% 17%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217 33% 17%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217 33% 17%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217 33% 17%;
    --input: 217 33% 17%;
    --ring: 221 83% 53%;

    /* Chart colors - professional palette */
    --chart-1: 221 83% 53%;
    --chart-2: 34 100% 34%;
    --chart-3: 0 72% 51%;
    --chart-4: 262 83% 58%;
    --chart-5: 199 89% 48%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom Scrollbar Styles */
@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(71 85 105) rgb(30 41 59);
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: rgb(30 41 59);
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: rgb(71 85 105);
    border-radius: 3px;
    transition: background 0.2s ease;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: rgb(100 116 139);
  }
  
  .scrollbar-thumb-slate-600::-webkit-scrollbar-thumb {
    background: rgb(71 85 105);
  }
  
  .scrollbar-track-slate-800::-webkit-scrollbar-track {
    background: rgb(30 41 59);
  }
}

