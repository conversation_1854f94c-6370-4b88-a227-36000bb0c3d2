import { Search, RefreshCw } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { TableProps } from "./types"

export const BaseTable: React.FC<TableProps & {
  children: React.ReactNode
  title: string
}> = ({
  searchTerm,
  onSearch,
  onRefresh,
  isRefreshing,
  children,
  title
}) => {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Search className="w-4 h-4 text-gray-500" />
          <Input
            placeholder={`Search ${title}...`}
            value={searchTerm}
            onChange={onSearch}
            className="h-8 w-[150px] lg:w-[250px]"
          />
        </div>
        <Button
          variant="ghost"
          size="sm"
          className="ml-auto"
          onClick={onRefresh}
          disabled={isRefreshing}
        >
          <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
        </Button>
      </div>
      {children}
    </div>
  )
} 