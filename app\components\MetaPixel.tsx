"use client";
import { useEffect } from "react";
import { usePathname } from "next/navigation";
import Script from "next/script";

// Add type declaration for fbq
declare global {
  interface Window {
    fbq: any;
  }
}

const MetaPixel = () => {
  const pathname = usePathname();

  useEffect(() => {
    // Track page views on route changes
    if (typeof window !== "undefined" && window.fbq) {
      window.fbq("track", "PageView");
    }
  }, [pathname]);

  return (
    <>
      <Script
        id="facebook-pixel"
        strategy="lazyOnload"
        dangerouslySetInnerHTML={{
          __html: `
            !function(f,b,e,v,n,t,s)
            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window, document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '540894251816091');
            fbq('init', '4085608968426739');
            fbq('track', 'PageView');
          `,
        }}
      />
      <noscript>
        <img
          height="1"
          width="1"
          style={{ display: "none" }}
          src="https://www.facebook.com/tr?id=540894251816091&ev=PageView&noscript=1"
        />
        <img
          height="1"
          width="1"
          style={{ display: "none" }}
          src="https://www.facebook.com/tr?id=4085608968426739&ev=PageView&noscript=1"
        />
      </noscript>
    </>
  );
};

export default MetaPixel;
