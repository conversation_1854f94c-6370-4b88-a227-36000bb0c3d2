"use client";
import { useState, useEffect } from 'react';

// Removed 'use client' to make this a server component

// ... existing code ... 

// Top-level server component
export default function FaqSection() {
  return <FaqSectionClient />;
}

// Client subcomponent for all state/effect/animation logic
function FaqSectionClient() {
  // Throttle/Reduce any animation or array mapping here
  // Return the simplified JSX
  return (
    <section>
      {/* Simplified FaqSection content with reduced animation */}
      {/* ... */}
    </section>
  );
} 