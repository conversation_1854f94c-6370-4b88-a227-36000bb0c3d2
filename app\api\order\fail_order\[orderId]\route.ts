import { NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { sanitizeErrorResponse } from '@/app/config/api';

export async function POST(
  request: Request,
  { params }: { params: { orderId: string } }
) {
  try {
    const headersList = headers();
    const authHeader = headersList.get('Authorization');

    if (!authHeader) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { orderId } = params;
    const formData = await request.formData();

    // Use the proxy pattern instead of calling backend directly
    const response = await fetch(`/api/proxy/ordors/fail_order/${orderId}`, {
      method: 'POST',
      headers: {
        'Authorization': authHeader,
      },
      body: formData,
    });

    if (!response.ok) {
      const errorText = await response.text();
      const sanitizedErrorText = sanitizeErrorResponse(errorText);
      return NextResponse.json(
        { error: sanitizedErrorText },
        { status: response.status }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
