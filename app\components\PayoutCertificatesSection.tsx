'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import { useState } from 'react'
import { ChevronLeft, ChevronRight, Award, DollarSign, TrendingUp } from 'lucide-react'

export function PayoutCertificatesSection() {
  const [currentIndex, setCurrentIndex] = useState(0)

  const payoutCertificates = [
    {
      name: "Kabiru Sani",
      image: "/payout/Kabiru Sani payout certificate-02.jpg",
      amount: "$12,450",
      profit: "+24.5%",
      date: "Recent"
    },
    {
      name: "<PERSON><PERSON>",
      image: "/payout/Farhad Mehrabi payout certificate-02.jpg",
      amount: "$8,320",
      profit: "+18.2%",
      date: "Recent"
    },
    {
      name: "<PERSON> Okello",
      image: "/payout/Musa Okello payout certificate-02-02.jpg",
      amount: "$15,780",
      profit: "+31.4%",
      date: "Recent"
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      image: "/payout/<PERSON><PERSON><PERSON> payout certificate-02.jpg",
      amount: "$6,940",
      profit: "+15.8%",
      date: "Recent"
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      image: "/payout/<PERSON><PERSON><PERSON> payout certificate-02.jpg",
      amount: "$9,650",
      profit: "+22.1%",
      date: "Recent"
    },
    {
      name: "Emre Kaya",
      image: "/payout/Emre Kaya payout certificate-02.jpg",
      amount: "$11,200",
      profit: "+28.7%",
      date: "Recent"
    },
    {
      name: "Omar Benyamina",
      image: "/payout/Omar Benyamina payout certificate-02.jpg",
      amount: "$7,890",
      profit: "+19.3%",
      date: "Recent"
    },
    {
      name: "Faisal Khan",
      image: "/payout/Faisal Khan payout certificate-02.jpg",
      amount: "$13,450",
      profit: "+26.8%",
      date: "Recent"
    },
    {
      name: "Abubakar Nsubuga",
      image: "/payout/Abubakar Nsubuga payout certificate-02.jpg",
      amount: "$10,120",
      profit: "+21.4%",
      date: "Recent"
    },
    {    name: "Hassan Dogo",
      image: "/payout/Hassan Dogo payout certificate-02.jpg",
      amount: "$8,760",
      profit: "+17.9%",
      date: "Recent"
    },
    {
      name: "Ismail Geddi",
      image: "/payout/Ismail Geddi  payout certificate-02.jpg",
      amount: "$14,230",
      profit: "+29.2%",
      date: "Recent"
    }
  ]

  const nextSlide = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === payoutCertificates.length - 1 ? 0 : prevIndex + 1
    )
  }

  const prevSlide = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? payoutCertificates.length - 1 : prevIndex - 1
    )
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const cardVariants = {
    hidden: { opacity: 0, y: 30, scale: 0.9 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  }

  const imageVariants = {
    hidden: { scale: 1.1, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut"
      }
    }
  }

  return (
    <section className="py-20 relative overflow-hidden bg-gradient-to-b from-[#0A0F1C] to-black">
      {/* Enhanced Background Effects */}
      <div className="absolute inset-0">
        <motion.div
          animate={{
            opacity: [0.1, 0.3, 0.1],
            scale: [1, 1.1, 1],
          }}
          transition={{ duration: 8, repeat: Infinity }}
          className="absolute top-0 right-0 w-[600px] h-[600px] bg-orange-500/10 rounded-full blur-[120px]"
        />
        <motion.div
          animate={{
            opacity: [0.1, 0.2, 0.1],
            scale: [1.1, 1, 1.1],
          }}
          transition={{ duration: 8, repeat: Infinity, delay: 4 }}
          className="absolute bottom-0 left-0 w-[600px] h-[600px] bg-blue-500/10 rounded-full blur-[120px]"
        />
        <div className="absolute inset-0 bg-[url('/grid.svg')] opacity-5" />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="flex items-center justify-center gap-3 mb-4">
            <Award className="w-8 h-8 text-orange-400" />
            <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-orange-400 via-orange-500 to-blue-500 bg-clip-text text-transparent">
              Real Payout Certificates
            </h2>
            <Award className="w-8 h-8 text-blue-400" />
          </div>
          <p className="text-gray-300 text-lg md:text-xl max-w-3xl mx-auto">
            Authentic proof of successful payouts to our elite traders. Every certificate represents real profits earned through our platform.
          </p>
        </motion.div>

        {/* Main Certificate Display */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="relative max-w-4xl mx-auto mb-12"
        >
          <motion.div
            variants={cardVariants}
            className="relative bg-gradient-to-br from-[#1a1f2d]/90 to-[#252a3a]/90 backdrop-blur-xl rounded-2xl p-6 md:p-8 shadow-2xl border border-orange-500/20"
          >
            {/* Navigation Buttons */}
            <button
              onClick={prevSlide}
              className="absolute left-4 top-1/2 -translate-y-1/2 z-20 bg-black/50 hover:bg-black/70 text-white p-3 rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110"
            >
              <ChevronLeft className="w-6 h-6" />
            </button>
            
            <button
              onClick={nextSlide}
              className="absolute right-4 top-1/2 -translate-y-1/2 z-20 bg-black/50 hover:bg-black/70 text-white p-3 rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110"
            >
              <ChevronRight className="w-6 h-6" />
            </button>

            {/* Certificate Image */}
            <div className="relative overflow-hidden rounded-xl">
              <motion.div
                key={currentIndex}
                variants={imageVariants}
                initial="hidden"
                animate="visible"
                className="relative aspect-[4/3] w-full"
              >
                <Image
                  src={payoutCertificates[currentIndex].image}
                  alt={`${payoutCertificates[currentIndex].name} Payout Certificate`}
                  fill
                  className="object-cover"
                  priority
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent" />
              </motion.div>
            </div>

            {/* Certificate Info */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="mt-6 text-center"
            >
              <h3 className="text-2xl md:text-3xl font-bold text-white mb-2">
                {payoutCertificates[currentIndex].name}
              </h3>
              <div className="flex items-center justify-center gap-6 mb-4">
                <div className="flex items-center gap-2 bg-orange-500/20 px-4 py-2 rounded-full">
                  <DollarSign className="w-5 h-5 text-orange-400" />
                  <span className="text-xl font-bold text-white">
                    {payoutCertificates[currentIndex].amount}
                  </span>
                </div>
                <div className="flex items-center gap-2 bg-green-500/20 px-4 py-2 rounded-full">
                  <TrendingUp className="w-5 h-5 text-green-400" />
                  <span className="text-lg font-semibold text-green-400">
                    {payoutCertificates[currentIndex].profit}
                  </span>
                </div>
              </div>
              <p className="text-gray-400 text-sm">
                Certificate #{currentIndex + 1} of {payoutCertificates.length}
              </p>
            </motion.div>
          </motion.div>
        </motion.div>

        {/* Thumbnail Gallery - replaced with animated marquee rows */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.4 }}
          className="max-w-7xl mx-auto my-20"
        >
          <h3 className="text-3xl md:text-5xl font-extrabold text-white text-center mb-8 leading-tight">
            All Payout <br />
            <span className="bg-gradient-to-r from-orange-500 via-orange-400 to-blue-600 bg-clip-text text-transparent">Certificates</span>
          </h3>
          <div className="space-y-8">
            {/* First Row: left-to-right */}
            <div className="overflow-hidden w-full">
              <motion.div
                className="flex gap-8"
                animate={{ x: [0, '-50%'] }}
                transition={{
                  repeat: Infinity,
                  repeatType: 'loop',
                  duration: 30,
                  ease: 'linear',
                }}
                style={{ width: 'max-content' }}
              >
                {[...payoutCertificates, ...payoutCertificates].map((certificate, idx) => (
                  <div
                    key={`row1-${idx}`}
                    onClick={() => setCurrentIndex(idx % payoutCertificates.length)}
                    className="relative cursor-pointer rounded-xl overflow-hidden border-4 border-white/10 hover:border-orange-500 transition-all duration-300 shadow-lg min-w-[260px] max-w-[320px] w-[18vw] bg-white/5 backdrop-blur-xl"
                  >
                    <div className="aspect-[4/3] relative">
                      <Image
                        src={certificate.image}
                        alt={`${certificate.name} Certificate`}
                        fill
                        className="object-cover"
                        loading="lazy"
                      />
                      <div className="absolute inset-0 bg-black/20 hover:bg-black/10 transition-colors duration-300" />
                      <div className="absolute bottom-2 left-2 right-2">
                        <div className="bg-black/70 backdrop-blur-sm rounded px-2 py-1">
                          <p className="text-white text-base font-semibold truncate">
                            {certificate.name}
                          </p>
                          <p className="text-orange-400 text-base font-bold">
                            {certificate.amount}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </motion.div>
            </div>
            {/* Second Row: right-to-left (antiparallel) */}
            <div className="overflow-hidden w-full">
              <motion.div
                className="flex gap-8"
                animate={{ x: ['-50%', 0] }}
                transition={{
                  repeat: Infinity,
                  repeatType: 'loop',
                  duration: 30,
                  ease: 'linear',
                }}
                style={{ width: 'max-content' }}
              >
                {[...payoutCertificates, ...payoutCertificates].map((certificate, idx) => (
                  <div
                    key={`row2-${idx}`}
                    onClick={() => setCurrentIndex(idx % payoutCertificates.length)}
                    className="relative cursor-pointer rounded-xl overflow-hidden border-4 border-white/10 hover:border-blue-500 transition-all duration-300 shadow-lg min-w-[260px] max-w-[320px] w-[18vw] bg-white/5 backdrop-blur-xl"
                  >
                    <div className="aspect-[4/3] relative">
                      <Image
                        src={certificate.image}
                        alt={`${certificate.name} Certificate`}
                        fill
                        className="object-cover"
                        loading="lazy"
                      />
                      <div className="absolute inset-0 bg-black/20 hover:bg-black/10 transition-colors duration-300" />
                      <div className="absolute bottom-2 left-2 right-2">
                        <div className="bg-black/70 backdrop-blur-sm rounded px-2 py-1">
                          <p className="text-white text-base font-semibold truncate">
                            {certificate.name}
                          </p>
                          <p className="text-orange-400 text-base font-bold">
                            {certificate.amount}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </motion.div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
} 