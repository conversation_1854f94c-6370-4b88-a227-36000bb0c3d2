"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  Shield, 
  Key, 
  Bell, 
  Globe, 
  Save,
  Camera,
  Edit3,
  CheckCircle,
  AlertCircle,
  Gift,
  Award,
  Copy,
  ExternalLink
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { toast } from "react-toastify"
import { getApiUrl, getEndpointPath, secureFetch } from "../../config/api"
import secureStorageAPI from '@/app/lib/secureStorage'

interface UserReferral {
  id: number
  user_id: number
  referral_code: string
  total_points: number
  created_at: string
  updated_at: string
}

interface UserProfile {
  id: number
  username: string
  email: string
  name: string
  country: string
  phone_no: string
  address: string
  created_at: string
  referred_by: string | null
  user_referral: UserReferral
  is_verified: boolean
}

interface NotificationSettings {
  emailNotifications: boolean
  smsNotifications: boolean
  pushNotifications: boolean
  tradeAlerts: boolean
  newsUpdates: boolean
  marketingEmails: boolean
}

export default function ProfilePage() {
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [notifications, setNotifications] = useState<NotificationSettings>({
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    tradeAlerts: true,
    newsUpdates: false,
    marketingEmails: false
  })
  const [isEditing, setIsEditing] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [editedProfile, setEditedProfile] = useState<Partial<UserProfile>>({})

  useEffect(() => {
    fetchProfileData()
  }, [])

  const fetchProfileData = async () => {
    try {
      setIsLoading(true)
      const token = secureStorageAPI.getItem('access_token')
      
      if (!token) {
        console.error('No access token found')
        return
      }

      const response = await secureFetch('auth/user/me')
      
      if (response.ok) {
        const data: UserProfile = await response.json()
        setProfile(data)
        setEditedProfile(data)
      } else {
        console.error('Failed to fetch profile data')
        toast.error('Failed to load profile data')
      }
    } catch (error) {
      console.error('Error fetching profile:', error)
      toast.error('Error loading profile data')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSave = async () => {
    setIsSaving(true)
    try {
      // For now, just simulate success
      setProfile(prev => prev ? { ...prev, ...editedProfile } : null)
      setIsEditing(false)
      toast.success('Profile updated successfully!')
    } catch (error) {
      console.error('Error updating profile:', error)
      toast.error('Failed to update profile')
    } finally {
      setIsSaving(false)
    }
  }

  const handleCancel = () => {
    setEditedProfile(profile || {})
    setIsEditing(false)
  }

  const handleInputChange = (field: keyof UserProfile, value: string) => {
    setEditedProfile(prev => ({ ...prev, [field]: value }))
  }

  const handleNotificationChange = (field: keyof NotificationSettings, value: boolean) => {
    setNotifications(prev => ({ ...prev, [field]: value }))
  }

  const copyReferralCode = () => {
    if (profile?.user_referral?.referral_code) {
      navigator.clipboard.writeText(profile.user_referral.referral_code)
      toast.success('Referral code copied to clipboard!')
    }
  }

  const VerificationBadge = ({ verified, type }: { verified: boolean, type: string }) => (
    <div className={`flex items-center gap-2 px-3 py-1.5 rounded-full text-xs font-medium ${
      verified 
        ? 'bg-green-500/15 text-green-400 border border-green-500/25' 
        : 'bg-yellow-500/15 text-yellow-400 border border-yellow-500/25'
    }`}>
      {verified ? <CheckCircle size={14} /> : <AlertCircle size={14} />}
      {verified ? `${type} Verified` : `${type} Pending`}
    </div>
  )

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  if (isLoading) {
    return (
      <div className="p-6 flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-slate-400">Loading profile data...</p>
        </div>
      </div>
    )
  }

  if (!profile) {
    return (
      <div className="p-6 flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <AlertCircle size={48} className="text-red-400 mx-auto mb-4" />
          <p className="text-slate-400">Failed to load profile data</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen relative bg-gradient-to-br from-blue-950 via-slate-900 to-orange-950 text-white overflow-x-hidden">
      {/* Animated background shapes */}
      <div className="pointer-events-none select-none absolute inset-0 z-0">
        <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-500/20 to-orange-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-tr from-orange-500/20 to-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute top-1/2 left-1/2 w-[600px] h-[600px] bg-gradient-to-r from-blue-500/10 to-orange-500/10 rounded-full blur-3xl animate-pulse delay-500 -translate-x-1/2 -translate-y-1/2" />
      </div>
      <div className="container mx-auto px-4 py-8 md:py-16 relative z-10">
        <div className="max-w-6xl mx-auto shadow-2xl rounded-3xl bg-gradient-to-br from-blue-900/80 via-slate-900/80 to-orange-900/80 border-2 border-blue-500/20 backdrop-blur-xl p-2 md:p-4">
          <div className="p-6 space-y-6">
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex items-center justify-between"
            >
              <div>
                <h1 className="text-3xl font-bold text-slate-100">Profile Settings</h1>
                <p className="text-slate-400 mt-1">Manage your account information and preferences</p>
              </div>
              <div className="flex gap-3">
                {isEditing ? (
                  <>
                    <Button
                      variant="outline"
                      onClick={handleCancel}
                      className="border-slate-600 text-slate-300 hover:bg-slate-800"
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleSave}
                      disabled={isSaving}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      {isSaving ? 'Saving...' : 'Save Changes'}
                    </Button>
                  </>
                ) : (
                  <Button
                    onClick={() => setIsEditing(true)}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    <Edit3 size={16} className="mr-2" />
                    Edit Profile
                  </Button>
                )}
              </div>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Profile Information */}
              <div className="lg:col-span-2 space-y-6">
                {/* Basic Information */}
                <Card className="bg-slate-800/50 border-slate-700">
                  <CardHeader>
                    <CardTitle className="text-slate-100 flex items-center gap-2">
                      <User size={20} />
                      Basic Information
                    </CardTitle>
                    <CardDescription className="text-slate-400">
                      Your personal and contact details
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="name" className="text-slate-300">Full Name</Label>
                        <Input
                          id="name"
                          value={isEditing ? (editedProfile.name || profile.name) : profile.name}
                          onChange={(e) => handleInputChange('name', e.target.value)}
                          disabled={!isEditing}
                          className="bg-slate-700/50 border-slate-600 text-slate-100"
                        />
                      </div>
                      <div>
                        <Label htmlFor="username" className="text-slate-300">Username</Label>
                        <Input
                          id="username"
                          value={profile.username}
                          disabled
                          className="bg-slate-700/50 border-slate-600 text-slate-100"
                        />
                      </div>
                      <div>
                        <Label htmlFor="email" className="text-slate-300">Email Address</Label>
                        <div className="flex items-center gap-2">
                          <Input
                            id="email"
                            value={profile.email}
                            disabled
                            className="bg-slate-700/50 border-slate-600 text-slate-100"
                          />
                          <VerificationBadge verified={profile.is_verified} type="Email" />
                        </div>
                      </div>
                      <div>
                        <Label htmlFor="phone" className="text-slate-300">Phone Number</Label>
                        <Input
                          id="phone"
                          value={isEditing ? (editedProfile.phone_no || profile.phone_no) : profile.phone_no}
                          onChange={(e) => handleInputChange('phone_no', e.target.value)}
                          disabled={!isEditing}
                          className="bg-slate-700/50 border-slate-600 text-slate-100"
                        />
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="country" className="text-slate-300">Country</Label>
                        <Input
                          id="country"
                          value={isEditing ? (editedProfile.country || profile.country) : profile.country}
                          onChange={(e) => handleInputChange('country', e.target.value)}
                          disabled={!isEditing}
                          className="bg-slate-700/50 border-slate-600 text-slate-100"
                        />
                      </div>
                      <div>
                        <Label htmlFor="address" className="text-slate-300">Address</Label>
                        <Input
                          id="address"
                          value={isEditing ? (editedProfile.address || profile.address) : profile.address}
                          onChange={(e) => handleInputChange('address', e.target.value)}
                          disabled={!isEditing}
                          className="bg-slate-700/50 border-slate-600 text-slate-100"
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Account Information */}
                <Card className="bg-slate-800/50 border-slate-700">
                  <CardHeader>
                    <CardTitle className="text-slate-100 flex items-center gap-2">
                      <Shield size={20} />
                      Account Information
                    </CardTitle>
                    <CardDescription className="text-slate-400">
                      Your account details and verification status
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label className="text-slate-300">User ID</Label>
                        <Input
                          value={profile.id.toString()}
                          disabled
                          className="bg-slate-700/50 border-slate-600 text-slate-100"
                        />
                      </div>
                      <div>
                        <Label className="text-slate-300">Member Since</Label>
                        <Input
                          value={formatDate(profile.created_at)}
                          disabled
                          className="bg-slate-700/50 border-slate-600 text-slate-100"
                        />
                      </div>
                    </div>
                    {profile.referred_by && (
                      <div>
                        <Label className="text-slate-300">Referred By</Label>
                        <Input
                          value={profile.referred_by}
                          disabled
                          className="bg-slate-700/50 border-slate-600 text-slate-100"
                        />
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Sidebar */}
              <div className="space-y-6">
                {/* Profile Picture */}
                <Card className="bg-slate-800/50 border-slate-700">
                  <CardContent className="p-6">
                    <div className="text-center">
                      <div className="relative inline-block">
                        <Avatar className="w-24 h-24 border-4 border-blue-500/30">
                          <AvatarFallback className="bg-blue-600 text-white text-2xl font-bold">
                            {profile.name.charAt(0).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        {isEditing && (
                          <Button
                            size="sm"
                            variant="outline"
                            className="absolute -bottom-2 -right-2 w-8 h-8 p-0 rounded-full border-slate-600 bg-slate-700"
                          >
                            <Camera size={14} />
                          </Button>
                        )}
                      </div>
                      <h3 className="text-lg font-semibold text-slate-100 mt-4">
                        {profile.name}
                      </h3>
                      <p className="text-slate-400 text-sm">@{profile.username}</p>
                      <p className="text-slate-400 text-sm">Member since {formatDate(profile.created_at)}</p>
                      <VerificationBadge verified={profile.is_verified} type="Account" />
                    </div>
                  </CardContent>
                </Card>

                {/* Referral Program */}
                <Card className="bg-slate-800/50 border-slate-700">
                  <CardHeader>
                    <CardTitle className="text-slate-100 flex items-center gap-2">
                      <Gift size={20} />
                      Referral Program
                    </CardTitle>
                    <CardDescription className="text-slate-400">
                      Share your referral code and earn rewards
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label className="text-slate-300 text-sm">Your Referral Code</Label>
                      <div className="flex items-center gap-2 mt-2">
                        <Input
                          value={profile.user_referral.referral_code}
                          disabled
                          className="bg-slate-700/50 border-slate-600 text-slate-100 font-mono"
                        />
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={copyReferralCode}
                          className="border-slate-600 text-slate-300 hover:bg-slate-700"
                        >
                          <Copy size={14} />
                        </Button>
                      </div>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                      <div>
                        <p className="text-slate-200 text-sm font-medium">Total Points</p>
                        <p className="text-slate-400 text-xs">Earned from referrals</p>
                      </div>
                      <div className="text-right">
                        <p className="text-2xl font-bold text-blue-400">{profile.user_referral.total_points}</p>
                        <p className="text-slate-400 text-xs">points</p>
                      </div>
                    </div>
                    <Button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                      <ExternalLink size={16} className="mr-2" />
                      Share Referral Link
                    </Button>
                  </CardContent>
                </Card>

                {/* Notification Settings */}
                <Card className="bg-slate-800/50 border-slate-700">
                  <CardHeader>
                    <CardTitle className="text-slate-100 flex items-center gap-2">
                      <Bell size={20} />
                      Notifications
                    </CardTitle>
                    <CardDescription className="text-slate-400">
                      Manage your notification preferences
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-slate-200 text-sm font-medium">Email Notifications</p>
                        <p className="text-slate-400 text-xs">Receive updates via email</p>
                      </div>
                      <Switch
                        checked={notifications.emailNotifications}
                        onCheckedChange={(checked) => handleNotificationChange('emailNotifications', checked)}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-slate-200 text-sm font-medium">SMS Notifications</p>
                        <p className="text-slate-400 text-xs">Receive updates via SMS</p>
                      </div>
                      <Switch
                        checked={notifications.smsNotifications}
                        onCheckedChange={(checked) => handleNotificationChange('smsNotifications', checked)}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-slate-200 text-sm font-medium">Trade Alerts</p>
                        <p className="text-slate-400 text-xs">Get notified about trade events</p>
                      </div>
                      <Switch
                        checked={notifications.tradeAlerts}
                        onCheckedChange={(checked) => handleNotificationChange('tradeAlerts', checked)}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-slate-200 text-sm font-medium">News Updates</p>
                        <p className="text-slate-400 text-xs">Receive market news and updates</p>
                      </div>
                      <Switch
                        checked={notifications.newsUpdates}
                        onCheckedChange={(checked) => handleNotificationChange('newsUpdates', checked)}
                      />
                    </div>
                  </CardContent>
                </Card>

                {/* Security */}
                <Card className="bg-slate-800/50 border-slate-700">
                  <CardHeader>
                    <CardTitle className="text-slate-100 flex items-center gap-2">
                      <Shield size={20} />
                      Security
                    </CardTitle>
                    <CardDescription className="text-slate-400">
                      Manage your account security
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <Button variant="outline" className="w-full border-slate-600 text-slate-300 hover:bg-slate-700">
                      <Key size={16} className="mr-2" />
                      Change Password
                    </Button>
                    <Button variant="outline" className="w-full border-slate-600 text-slate-300 hover:bg-slate-700">
                      <Shield size={16} className="mr-2" />
                      Two-Factor Auth
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 