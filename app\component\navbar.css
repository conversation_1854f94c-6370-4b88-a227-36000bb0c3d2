/* Professional navbar styles */

/* Smooth transition for navbar background */
.navbar {
  transition: all 0.3s ease;
  box-shadow: 0 2px 16px 0 rgba(0,0,0,0.08);
}

/* Logo hover effect */
.logo-container {
  transition: transform 0.3s cubic-bezier(.4,0,.2,1);
}

.logo-container:focus,
.logo-container:hover {
  transform: scale(1.08);
  outline: none;
}

.nav-active {
  position: relative;
}

.nav-active::after {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  bottom: -2px;
  width: 100%;
  height: 3px;
  border-radius: 2px;
  background: linear-gradient(90deg, #ff8800 0%, #2563eb 100%);
  transition: width 0.3s cubic-bezier(.4,0,.2,1);
}

/* Media queries for responsive adjustments */
@media (max-width: 640px) {
  .navbar {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
}
