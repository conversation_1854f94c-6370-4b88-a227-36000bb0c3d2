"use client";
import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Award, TrendingUp, Users, DollarSign, Star, ArrowRight } from 'lucide-react'
import Image from 'next/image'

function useIsIPhone() {
  const [isIPhone, setIsIPhone] = useState(false);
  useEffect(() => {
    setIsIPhone(/iPhone|iPad|iPod/.test(navigator.userAgent));
  }, []);
  return isIPhone;
}

// Top-level server component
export function EnhancedPayoutSection() {
  const isIPhone = useIsIPhone();
  return <EnhancedPayoutSectionClient isIPhone={isIPhone} />;
}

function EnhancedPayoutSectionClient({ isIPhone }) {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  // Show loading state until client-side hydration is complete
  if (!isClient) {
    return (
      <section className="py-16 bg-gradient-to-b from-[#0A0F1C] to-black">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="animate-pulse bg-white/5 rounded-lg h-32 flex items-center justify-center">
              <span className="text-gray-400">Loading Payout Section...</span>
            </div>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className={`${isIPhone ? 'min-h-screen-ios' : 'min-h-screen'} relative py-16 md:py-20 bg-gradient-to-b from-[#0A0F1C] to-black overflow-hidden`}>
      {/* Background effects - reduced for mobile */}
      <div className="absolute inset-0">
        <div className="absolute w-[400px] md:w-[800px] h-[400px] md:h-[800px] -left-1/4 -top-1/4 bg-orange-500/10 rounded-full blur-[40px] md:blur-[80px]" />
        <div className="absolute w-[400px] md:w-[800px] h-[400px] md:h-[800px] -right-1/4 -bottom-1/4 bg-blue-500/10 rounded-full blur-[40px] md:blur-[80px]" />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12 md:mb-16"
        >
          <div className="inline-flex items-center gap-2 bg-gradient-to-r from-orange-500/10 to-blue-500/10 border border-orange-500/20 rounded-full px-4 md:px-6 py-2 mb-4 md:mb-6">
            <Award className="w-4 md:w-5 h-4 md:h-5 text-orange-400" />
            <span className="text-orange-400 text-xs md:text-sm font-medium">Success Stories</span>
          </div>
          
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 md:mb-6">
            <span className="text-white">Recent </span>
            <span className="bg-gradient-to-r from-orange-500 to-blue-600 bg-clip-text text-transparent">Payouts</span>
          </h2>
          
          <p className="text-gray-300 text-sm md:text-lg max-w-3xl mx-auto">
            See our successful traders receiving their well-earned profits. Join thousands of traders who have already achieved their financial goals.
          </p>
        </motion.div>

        {/* Certificate Images Grid - Only Images */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6 max-w-6xl mx-auto mb-12 md:mb-16">
          {[
            "Abubakar Nsubuga payout certificate-02.jpg",
            "Ajmal Haidar payout certificate-02.jpg",
            "Emre Kaya payout certificate-02.jpg",
            "Faisal Khan payout certificate-02.jpg",
            "Farhad Mehrabi payout certificate-02.jpg",
            "Hassan Dogo payout certificate-02.jpg",
            "Ismail Geddi  payout certificate-02.jpg",
            "Kabiru Sani payout certificate-02.jpg",
            "Musa Okello payout certificate-02-02.jpg",
            "Omar Benyamina payout certificate-02.jpg",
            "Tariq Anwar payout certificate-02.jpg"
          ].map((image, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.05 }}
              whileHover={{ scale: 1.05, y: -5 }}
              className="group relative"
            >
              <div className="relative overflow-hidden rounded-lg md:rounded-xl border border-white/10 shadow-lg hover:border-orange-500/30 transition-all duration-300">
                <Image
                  src={`/payout/${image}`}
                  alt={`Payout Certificate ${index + 1}`}
                  width={300}
                  height={200}
                  className="w-full h-32 md:h-40 object-cover group-hover:scale-110 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Stats Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 max-w-4xl mx-auto mb-12 md:mb-16"
        >
          {[
            { icon: TrendingUp, value: "$450K+", label: "Total Payouts" },
            { icon: Users, value: "1,983", label: "Active Traders" },
            { icon: Award, value: "99%", label: "Success Rate" }
          ].map((stat, index) => (
            <div key={index} className="text-center">
              <div className="w-12 md:w-16 h-12 md:h-16 bg-gradient-to-r from-orange-500/20 to-blue-500/20 rounded-xl md:rounded-2xl flex items-center justify-center mx-auto mb-3 md:mb-4">
                <stat.icon className="w-6 md:w-8 h-6 md:h-8 text-orange-400" />
              </div>
              <div className="text-2xl md:text-3xl font-bold text-white mb-1 md:mb-2">{stat.value}</div>
              <div className="text-gray-400 text-sm md:text-base">{stat.label}</div>
            </div>
          ))}
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center"
        >
          <div className="bg-gradient-to-r from-orange-500/10 to-blue-500/10 rounded-xl md:rounded-2xl p-6 md:p-8 border border-orange-500/20">
            <h3 className="text-xl md:text-2xl font-bold text-white mb-3 md:mb-4">
              Ready to Start Your Success Story?
            </h3>
            <p className="text-gray-300 mb-4 md:mb-6 max-w-2xl mx-auto text-sm md:text-base">
              Join thousands of successful traders who have already achieved their financial goals with our platform.
            </p>
            <button className="bg-gradient-to-r from-orange-500 to-blue-600 text-white px-6 md:px-8 py-3 rounded-lg font-semibold hover:brightness-110 transition-all duration-300 flex items-center gap-2 mx-auto">
              Start Trading Now
              <ArrowRight className="w-4 h-4" />
            </button>
          </div>
        </motion.div>
      </div>
    </section>
  )
} 