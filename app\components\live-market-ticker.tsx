"use client"

import { motion, useAnimationControls } from "framer-motion"
import { TrendingUp, TrendingDown } from "lucide-react"
import { useEffect, useState } from "react"
import { useIsMobile } from "@/hooks/use-mobile"

// Top-level server component
export function LiveMarketTicker() {
  return <LiveMarketTickerClient />;
}

// iPhone detection hook
function useIsIPhone() {
  const [isIPhone, setIsIPhone] = useState(false);
  useEffect(() => {
    setIsIPhone(/iPhone|iPad|iPod/.test(navigator.userAgent));
  }, []);
  return isIPhone;
}

// Client subcomponent for all state/effect/animation logic
function LiveMarketTickerClient() {
  const isMobile = useIsMobile()
  const isIPhone = useIsIPhone()
  const [displayCount, setDisplayCount] = useState(6)
  const [isClient, setIsClient] = useState(false)
  const [isVisible, setIsVisible] = useState(false)

  // Set isClient to true when component mounts (client-side only)
  useEffect(() => {
    setIsClient(true)
    // Add a small delay to ensure smooth animation (longer for iPhone)
    const timer = setTimeout(() => setIsVisible(true), isIPhone ? 300 : 100)
    return () => clearTimeout(timer)
  }, [isIPhone])

  useEffect(() => {
    // Reduce the number of displayed items on mobile for better performance
    if (isIPhone) {
      setDisplayCount(1) // Only 1 item for iPhone to prevent overheating
    } else if (isMobile) {
      setDisplayCount(2) // Reduced from 3 to 2 for better performance
    } else {
      setDisplayCount(6)
    }
  }, [isMobile, isIPhone])

  const marketData = [
    { symbol: "EUR/USD", price: "1.0876", change: "+0.15%" },
    { symbol: "BTC/USD", price: "43,250", change: "-0.32%" },
    { symbol: "Gold", price: "2,023.50", change: "+0.45%" },
    { symbol: "S&P 500", price: "4,783.25", change: "+0.28%" },
    { symbol: "NASDAQ", price: "14,972.76", change: "+0.55%" },
    { symbol: "USD/JPY", price: "148.15", change: "-0.22%" },
  ]

  const controls = useAnimationControls()

  useEffect(() => {
    if (!isClient) return

    const animate = async () => {
      try {
        // Disable animation on iPhone to prevent overheating
        if (isIPhone) {
          // Static display for iPhone
          await controls.start({
            x: "0%",
            transition: { duration: 0 }
          })
          return
        }

        // Slower animation for mobile to improve performance
        const duration = isMobile ? 30 : 20

        while (true) {
          await controls.start({
            x: ["0%", "-50%"],
            transition: {
              duration: duration,
              ease: "linear",
              repeat: Infinity,
            },
          })
        }
      } catch (error) {
        console.warn("Animation error:", error)
      }
    }
    animate()
  }, [controls, isClient, isMobile, isIPhone])

  // Filter the market data based on display count
  const displayedMarketData = marketData.slice(0, displayCount)

  // Only render on client-side to avoid hydration issues
  if (!isClient) {
    return (
      <div className="w-full bg-black/50 backdrop-blur-md z-50 py-2 overflow-hidden">
        <div className="container mx-auto px-4">
          <div className="flex gap-4 md:gap-8 items-center whitespace-nowrap">
            <div className="text-white/80">Loading market data...</div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full bg-black/50 backdrop-blur-md z-50 py-2 overflow-hidden">
      <div className="container mx-auto px-4">
        <motion.div
          className="flex gap-4 md:gap-8 items-center whitespace-nowrap"
          animate={isVisible ? controls : false}
          initial={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
        >
          {[...displayedMarketData, ...displayedMarketData].map((item, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : -20 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-center gap-1 sm:gap-2 md:gap-3"
            >
              <span className="text-xs sm:text-sm md:text-base text-white/80">{item.symbol}</span>
              <span className="text-xs sm:text-sm md:text-base text-white font-medium">{item.price}</span>
              <span className={`flex items-center text-xs sm:text-sm md:text-base ${
                item.change.startsWith("+") ? "text-green-400" : "text-red-400"
              }`}>
                {item.change.startsWith("+") ?
                  <TrendingUp className="w-2 h-2 sm:w-3 sm:h-3 md:w-4 md:h-4" /> :
                  <TrendingDown className="w-2 h-2 sm:w-3 sm:h-3 md:w-4 md:h-4" />
                }
                {item.change}
              </span>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </div>
  )
}