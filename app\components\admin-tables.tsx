import { useState, useEffect, useRef } from "react"
import { motion } from "framer-motion"
import { Search, XCircle, CreditCard, Hash, Shield, LineChart, ChevronDown, User, Wallet, AlertTriangle, MoreVertical, Eye, EyeOff, Image } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { EditOrderModal } from "@/components/edit-order-modal"
import { OrderStatus, OrderType, RejectReasons, FailReasons, OrderDetails } from "@/types/order"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { ViewOrderModal } from "@/components/view-order-modal"
import { FailOrderModal } from "@/components/fail-order-modal"
import { PassOrderModal } from "@/components/pass-order-modal"
import { getApiUrl, getEndpointPath } from "../config/api"

interface AdminTablesProps {
  selectedSection: "orders" | "completedOrders" | "failedOrders" | "passOrders" | "stageTwoOrders" | "liveOrders" | "runningOrders" | "certificates" | "analytics" | "reports" | null;
}

interface RunningOrder {
  id: string;
  order_id: string;
  platform_login: string;
  platform_password: string;
  server: string;
  session_id: string;
  terminal_id: string;
  txid?: string;
  image?: {
    image_url: string;
    created_at: string;
  };
}

export function AdminTables({ selectedSection }: AdminTablesProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [orders, setOrders] = useState<OrderDetails[]>([])
  const [completedOrders, setCompletedOrders] = useState<OrderDetails[]>([])
  const [failedOrders, setFailedOrders] = useState<OrderDetails[]>([])
  const [passOrders, setPassOrders] = useState<OrderDetails[]>([])
  const [stageTwoOrders, setStageTwoOrders] = useState<OrderDetails[]>([])
  const [liveOrders, setLiveOrders] = useState<OrderDetails[]>([])
  const [runningOrders, setRunningOrders] = useState<RunningOrder[]>([])
  const [editedOrder, setEditedOrder] = useState<OrderDetails | null>(null)
  const [isMobile, setIsMobile] = useState(false)
  const [viewOrder, setViewOrder] = useState<OrderDetails | null>(null)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  // Fetch functions for all admin data
  const fetchRunningOrders = async () => {
    try {
      const response = await fetch('/api/proxy/ordors/running_orders');
      if (response.ok) {
        const data = await response.json();
        console.log('Running orders data:', data);
        setRunningOrders(Array.isArray(data) ? data : []);
      } else {
        console.error('Failed to fetch running orders:', response.status);
        setRunningOrders([]);
      }
    } catch (error) {
      console.error('Error fetching running orders:', error);
      setRunningOrders([]);
    }
  };

  const fetchCompletedOrders = async () => {
    try {
      const response = await fetch('/api/proxy/ordors/completed_orders');
      if (response.ok) {
        const data = await response.json();
        console.log('Completed orders data:', data);
        
        const mappedOrders = data.map((order: any) => ({
          id: order.complete_order_id || order.id || '',
          order_id: order.order_id || order.id || '',
          user: {
            name: order.username || order.name || 'N/A',
            email: order.email || 'N/A',
            password: order.password || undefined,
            hashed_password: order.hashed_password || undefined,
          },
          amount: order.account_size || order.amount || '0',
          status: 'Completed',
          type: order.type || 'STANDARD',
          createdAt: order.created_at || order.createdAt || new Date().toISOString(),
          accountType: order.challenge_type || order.accountType || 'Standard',
          platformType: order.platform || order.platformType || 'MT4',
          platformLogin: order.platform_login || order.platformLogin || 'N/A',
          platformPassword: order.platform_password || order.platformPassword || 'N/A',
          server: order.server || 'N/A',
          sessionId: order.session_id || order.sessionId || 'N/A',
          terminalId: order.terminal_id || order.terminalId || 'N/A',
          startingBalance: parseInt(order.account_size) || 0,
          currentBalance: parseInt(order.account_size) || 0,
          profitTarget: order.profit_target || order.profitTarget || 0,
          paymentMethod: order.payment_method || order.paymentMethod || 'N/A',
          txid: order.txid || 'N/A',
          image: order.image || null,
          paymentProof: order.payment_proof || order.paymentProof || null,
        }));
        
        setCompletedOrders(mappedOrders);
      } else {
        console.error('Failed to fetch completed orders:', response.status);
        setCompletedOrders([]);
      }
    } catch (error) {
      console.error('Error fetching completed orders:', error);
      setCompletedOrders([]);
    }
  };

  const fetchPassedOrders = async () => {
    try {
      const response = await fetch('/api/proxy/ordors/passed_orders');
      if (response.ok) {
        const data = await response.json();
        console.log('Passed orders data:', data);
        
        const mappedOrders = data.map((order: any) => ({
          id: order.pass_order_id || order.id || '',
          order_id: order.order_id || order.id || '',
          user: {
            name: order.username || order.name || 'N/A',
            email: order.email || 'N/A',
            password: order.password || undefined,
            hashed_password: order.hashed_password || undefined,
          },
          amount: order.account_size || order.amount || '0',
          status: 'Passed',
          type: order.type || 'STANDARD',
          createdAt: order.pass_date || order.created_at || new Date().toISOString(),
          accountType: order.challenge_type || order.accountType || 'Standard',
          platformType: order.platform || order.platformType || 'MT4',
          platformLogin: order.platform_login || order.platformLogin || 'N/A',
          platformPassword: order.platform_password || order.platformPassword || 'N/A',
          server: order.server || 'N/A',
          sessionId: order.session_id || order.sessionId || 'N/A',
          terminalId: order.terminal_id || order.terminalId || 'N/A',
          startingBalance: parseInt(order.account_size) || 0,
          currentBalance: parseInt(order.account_size) || 0,
          profitTarget: order.profit_amount || order.profitTarget || 0,
          paymentMethod: order.payment_method || order.paymentMethod || 'N/A',
          txid: order.txid || 'N/A',
          notes: order.notes || null,
          image: order.image || null,
          paymentProof: order.payment_proof || order.paymentProof || null,
        }));
        
        setPassOrders(mappedOrders);
      } else {
        console.error('Failed to fetch passed orders:', response.status);
        setPassOrders([]);
      }
    } catch (error) {
      console.error('Error fetching passed orders:', error);
      setPassOrders([]);
    }
  };

  const fetchStageTwoOrders = async () => {
    try {
      const response = await fetch('/api/proxy/ordors/stage_two_orders');
      if (response.ok) {
        const data = await response.json();
        console.log('Stage two orders data:', data);
        
        const mappedOrders = data.map((order: any) => ({
          id: order.id || '',
          order_id: order.order_id || order.id || '',
          user: {
            name: order.username || order.name || 'N/A',
            email: order.email || 'N/A',
            password: order.password || undefined,
            hashed_password: order.hashed_password || undefined,
          },
          amount: order.account_size || order.amount || '0',
          status: 'Stage Two',
          type: order.type || 'STANDARD',
          createdAt: order.created_at || order.createdAt || new Date().toISOString(),
          accountType: order.challenge_type || order.accountType || 'Standard',
          platformType: order.platform || order.platformType || 'MT4',
          platformLogin: order.platform_login || order.platformLogin || 'N/A',
          platformPassword: order.platform_password || order.platformPassword || 'N/A',
          server: order.server || 'N/A',
          sessionId: order.session_id || order.sessionId || 'N/A',
          terminalId: order.terminal_id || order.terminalId || 'N/A',
          startingBalance: parseInt(order.account_size) || 0,
          currentBalance: parseInt(order.account_size) || 0,
          profitTarget: order.profit_target || order.profitTarget || 0,
          paymentMethod: order.payment_method || order.paymentMethod || 'N/A',
          txid: order.txid || 'N/A',
          image: order.image || null,
          paymentProof: order.payment_proof || order.paymentProof || null,
        }));
        
        setStageTwoOrders(mappedOrders);
      } else {
        console.error('Failed to fetch stage two orders:', response.status);
        setStageTwoOrders([]);
      }
    } catch (error) {
      console.error('Error fetching stage two orders:', error);
      setStageTwoOrders([]);
    }
  };

  const fetchLiveOrders = async () => {
    try {
      const response = await fetch('/api/proxy/ordors/live_orders');
      if (response.ok) {
        const data = await response.json();
        console.log('Live orders data:', data);
        
        const mappedOrders = data.map((order: any) => ({
          id: order.id || '',
          order_id: order.order_id || order.id || '',
          user: {
            name: order.username || order.name || 'N/A',
            email: order.email || 'N/A',
            password: order.password || undefined,
            hashed_password: order.hashed_password || undefined,
          },
          amount: order.account_size || order.amount || '0',
          status: 'Live',
          type: order.type || 'STANDARD',
          createdAt: order.created_at || order.createdAt || new Date().toISOString(),
          accountType: order.challenge_type || order.accountType || 'Standard',
          platformType: order.platform || order.platformType || 'MT4',
          platformLogin: order.platform_login || order.platformLogin || 'N/A',
          platformPassword: order.platform_password || order.platformPassword || 'N/A',
          server: order.server || 'N/A',
          sessionId: order.session_id || order.sessionId || 'N/A',
          terminalId: order.terminal_id || order.terminalId || 'N/A',
          startingBalance: parseInt(order.account_size) || 0,
          currentBalance: parseInt(order.account_size) || 0,
          profitTarget: order.profit_target || order.profitTarget || 0,
          paymentMethod: order.payment_method || order.paymentMethod || 'N/A',
          txid: order.txid || 'N/A',
          image: order.image || null,
          paymentProof: order.payment_proof || order.paymentProof || null,
        }));
        
        setLiveOrders(mappedOrders);
      } else {
        console.error('Failed to fetch live orders:', response.status);
        setLiveOrders([]);
      }
    } catch (error) {
      console.error('Error fetching live orders:', error);
      setLiveOrders([]);
    }
  };

  const fetchFailedOrders = async () => {
    try {
      const response = await fetch('/api/proxy/ordors/failed_orders');
      if (response.ok) {
        const data = await response.json();
        console.log('Failed orders data:', data);
        
        const mappedOrders = data.map((order: any) => ({
          id: order.fail_order_id || order.id || '',
          order_id: order.order_id || order.id || '',
          user: {
            name: order.username || order.name || 'N/A',
            email: order.email || 'N/A',
            password: order.password || undefined,
            hashed_password: order.hashed_password || undefined,
          },
          amount: order.account_size || order.amount || '0',
          status: 'Failed',
          type: order.type || 'STANDARD',
          createdAt: order.date || order.created_at || new Date().toISOString(),
          accountType: order.challenge_type || order.accountType || 'Standard',
          platformType: order.platform || order.platformType || 'MT4',
          platformLogin: order.platform_login || order.platformLogin || 'N/A',
          platformPassword: order.platform_password || order.platformPassword || 'N/A',
          server: order.server || 'N/A',
          sessionId: order.session_id || order.sessionId || 'N/A',
          terminalId: order.terminal_id || order.terminalId || 'N/A',
          startingBalance: parseInt(order.account_size) || 0,
          currentBalance: parseInt(order.account_size) || 0,
          profitTarget: order.profit_target || order.profitTarget || 0,
          paymentMethod: order.payment_method || order.paymentMethod || 'N/A',
          txid: order.txid || 'N/A',
          reason: order.reason || null,
          image: order.image || null,
          paymentProof: order.payment_proof || order.paymentProof || null,
        }));
        
        setFailedOrders(mappedOrders);
      } else {
        console.error('Failed to fetch failed orders:', response.status);
        setFailedOrders([]);
      }
    } catch (error) {
      console.error('Error fetching failed orders:', error);
      setFailedOrders([]);
    }
  };

  const fetchOrders = async () => {
    try {
      const response = await fetch('/api/proxy/ordors/orders');
      if (response.ok) {
        const data = await response.json();
        console.log('Orders data:', data);
        
        // Log the first order to see the structure
        if (data.length > 0) {
          console.log('First order structure:', data[0]);
          console.log('First order keys:', Object.keys(data[0]));
        }
        
        // Map the backend data to match the OrderDetails interface
        const mappedOrders = data.map((order: any) => ({
          id: order.id || order.order_id || '',
          order_id: order.order_id || order.id || '',
          user: {
            name: order.username || order.name || 'N/A',
            email: order.email || 'N/A',
            password: order.password || undefined,
            hashed_password: order.hashed_password || undefined,
          },
          amount: order.account_size || order.amount || '0',
          status: order.status || 'Pending',
          type: order.type || 'STANDARD',
          createdAt: order.created_at || order.createdAt || new Date().toISOString(),
          accountType: order.challenge_type || order.accountType || 'Standard',
          platformType: order.platform || order.platformType || 'MT4',
          platformLogin: order.platform_login || order.platformLogin || 'N/A',
          platformPassword: order.platform_password || order.platformPassword || 'N/A',
          server: order.server || 'N/A',
          sessionId: order.session_id || order.sessionId || 'N/A',
          terminalId: order.terminal_id || order.terminalId || 'N/A',
          startingBalance: parseInt(order.account_size) || 0,
          currentBalance: parseInt(order.account_size) || 0,
          profitTarget: order.profit_target || order.profitTarget || 0,
          paymentMethod: order.payment_method || order.paymentMethod || 'N/A',
          txid: order.txid || 'N/A',
          image: order.image || null,
          paymentProof: order.payment_proof || order.paymentProof || null,
        }));
        
        console.log('Mapped orders:', mappedOrders);
        console.log('First mapped order:', mappedOrders[0]);
        setOrders(mappedOrders);
      } else {
        console.error('Failed to fetch orders:', response.status);
        setOrders([]);
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
      setOrders([]);
    }
  };

  // Load data based on selected section
  useEffect(() => {
    if (!selectedSection) return;

    console.log('Loading data for section:', selectedSection);
    
    switch (selectedSection) {
      case "runningOrders":
        fetchRunningOrders();
        break;
      case "orders":
        fetchOrders();
        break;
      case "completedOrders":
        fetchCompletedOrders();
        break;
      case "failedOrders":
        fetchFailedOrders();
        break;
      case "passOrders":
        fetchPassedOrders();
        break;
      case "stageTwoOrders":
        fetchStageTwoOrders();
        break;
      case "liveOrders":
        fetchLiveOrders();
        break;
      default:
        break;
    }
  }, [selectedSection]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
  }

  const handleConfirmOrder = async (order: OrderDetails) => {
    try {
      // ... existing confirm order logic ...
    } catch (error) {
      console.error('Error confirming order:', error)
    }
  }

  const handleSaveChanges = async (order: OrderDetails) => {
    try {
      // Implement save changes logic
      console.log('Saving changes for order:', order);
    } catch (error) {
      console.error('Error saving changes:', error);
    }
  };

  const handleRejectOrder = async (order: OrderDetails, reason: RejectReasons) => {
    try {
      const formData = new FormData();
      formData.append('reason', reason);
      const response = await fetch(getApiUrl(`order/reject_order/${order.id}`), {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        console.log(`Order ${order.id} rejected due to: ${reason}`);
      } else {
        throw new Error('Failed to reject order');
      }
    } catch (error) {
      console.error('Error rejecting order:', error);
    }
  };

  const handleFailOrder = async (order: OrderDetails, reason: FailReasons) => {
    try {
      const formData = new FormData();
      formData.append('reason', reason);
      formData.append('date', new Date().toISOString());
      
      const response = await fetch(getApiUrl(`order/fail_order/${order.order_id?.replace('FDH', '')}`), {
        method: "POST",
        body: formData
      });

      if (!response.ok) {
        throw new Error("Failed to fail order");
      }

      console.log("Order failed successfully");
    } catch (error) {
      console.error("Error failing order:", error);
    }
  };

  const handleViewOrder = (order: OrderDetails) => {
    setViewOrder(order);
  };

  const renderOrderActions = (order: OrderDetails) => {
    if (!mounted) return null;

    return (
      <div className="flex items-center gap-2">
        {selectedSection === "orders" && (
          <>
            <EditOrderModal
              order={order}
              onSave={handleSaveChanges}
              onReject={handleRejectOrder}
              onFail={handleFailOrder}
            />
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => handleConfirmOrder(order)}
              className="bg-blue-500 text-white hover:bg-blue-600 rounded-lg text-xs"
            >
              Confirm
            </Button>
          </>
        )}
        <ViewOrderModal
          order={order}
          onView={handleViewOrder}
          status={order.status}
        />
      </div>
    );
  };

  const itemsPerPage = 10;
  const [currentPage, setCurrentPage] = useState(1);

  const renderPaginationControls = () => {
    if (!mounted) return null;

    const totalPages = Math.ceil(
      (selectedSection === "runningOrders" ? runningOrders.length :
       selectedSection === "orders" ? orders.length :
       selectedSection === "completedOrders" ? completedOrders.length :
       selectedSection === "failedOrders" ? failedOrders.length :
       selectedSection === "passOrders" ? passOrders.length :
       selectedSection === "stageTwoOrders" ? stageTwoOrders.length :
       selectedSection === "liveOrders" ? liveOrders.length : 0) / itemsPerPage
    );

    return (
      <div className="flex justify-between items-center mt-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
          disabled={currentPage === 1}
          className="text-xs"
        >
          Previous
        </Button>
        <span className="text-xs">
          Page {currentPage} of {totalPages}
        </span>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
          disabled={currentPage === totalPages}
          className="text-xs"
        >
          Next
        </Button>
      </div>
    );
  };

  const renderTable = () => {
    if (!mounted) return null;

    switch (selectedSection) {
      case "runningOrders":
        return (
          <div className="overflow-x-auto">
            <div className="mb-4">
              <Input
                placeholder="Search by order ID or platform login..."
                value={searchTerm}
                onChange={handleSearch}
                className="max-w-sm bg-[#1E3A5F]/20 border-[#1E3A5F] text-white text-xs"
              />
            </div>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-xs">Order ID</TableHead>
                  <TableHead className="text-xs">Platform Login</TableHead>
                  <TableHead className="text-xs">Server</TableHead>
                  <TableHead className="text-xs">Session ID</TableHead>
                  <TableHead className="text-xs">Terminal ID</TableHead>
                  <TableHead className="text-xs">Transaction ID</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {runningOrders.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage).map((order: RunningOrder) => (
                  <TableRow key={order.id}>
                    <TableCell className="text-xs font-medium">{order.order_id}</TableCell>
                    <TableCell className="text-xs">{order.platform_login}</TableCell>
                    <TableCell className="text-xs">{order.server}</TableCell>
                    <TableCell className="text-xs">{order.session_id}</TableCell>
                    <TableCell className="text-xs">{order.terminal_id}</TableCell>
                    <TableCell className="text-xs">{order.txid}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            {renderPaginationControls()}
          </div>
        );
      case "certificates":
        return (
          <div className="overflow-x-auto">
            <div className="mb-4">
              <Input
                placeholder="Search certificates..."
                value={searchTerm}
                onChange={handleSearch}
                className="max-w-sm bg-[#1E3A5F]/20 border-[#1E3A5F] text-white text-xs"
              />
            </div>
            <div className="text-center py-8">
              <p className="text-gray-400">Certificate management coming soon...</p>
            </div>
          </div>
        );
      case "orders":
      case "completedOrders":
      case "failedOrders":
      case "passOrders":
      case "stageTwoOrders":
      case "liveOrders":
        const orderData = selectedSection === "orders" ? orders :
                         selectedSection === "completedOrders" ? completedOrders :
                         selectedSection === "failedOrders" ? failedOrders :
                         selectedSection === "passOrders" ? passOrders :
                         selectedSection === "stageTwoOrders" ? stageTwoOrders :
                         selectedSection === "liveOrders" ? liveOrders : [];
        
        return (
          <div className="overflow-x-auto">
            <div className="mb-4">
              <Input
                placeholder="Search by name, email, payment method, transaction ID, or order ID..."
                value={searchTerm}
                onChange={handleSearch}
                className="max-w-sm bg-[#1E3A5F]/20 border-[#1E3A5F] text-white text-xs"
              />
            </div>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-xs">Order ID</TableHead>
                  <TableHead className="text-xs">Name</TableHead>
                  <TableHead className="text-xs">Account Type</TableHead>
                  <TableHead className="text-xs">Amount</TableHead>
                  <TableHead className="text-xs">Platform</TableHead>
                  <TableHead className="text-xs">Transaction ID</TableHead>
                  <TableHead className="text-xs">Status</TableHead>
                  {!isMobile && <TableHead className="text-xs">Created At</TableHead>}
                  <TableHead className="text-xs">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {orderData.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage).map((order: OrderDetails) => (
                  <TableRow key={order.id}>
                    <TableCell className="text-xs font-medium">{order.id}</TableCell>
                    <TableCell className="text-xs">{order.user?.name || 'N/A'}</TableCell>
                    <TableCell className="text-xs">{order.accountType || 'N/A'}</TableCell>
                    <TableCell className="text-xs">${order.amount || 0}</TableCell>
                    <TableCell className="text-xs">{order.platformType || 'N/A'}</TableCell>
                    <TableCell className="text-xs">{order.txid || 'N/A'}</TableCell>
                    <TableCell className="text-xs">{order.status || 'N/A'}</TableCell>
                    {!isMobile && <TableCell className="text-xs">{order.createdAt || 'N/A'}</TableCell>}
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {selectedSection === "orders" && (
                          <>
                            <EditOrderModal
                              order={order}
                              onSave={handleSaveChanges}
                              onReject={handleRejectOrder}
                              onFail={handleFailOrder}
                            />
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => handleConfirmOrder(order)}
                              className="bg-blue-500 text-white hover:bg-blue-600 rounded-lg text-xs"
                            >
                              Confirm
                            </Button>
                          </>
                        )}
                        <ViewOrderModal
                          order={order}
                          onView={handleViewOrder}
                          status={order.status}
                        />
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            {renderPaginationControls()}
          </div>
        );
      default:
        return null;
    }
  };

  if (!mounted) {
    return null;
  }

  return (
    <motion.div
      className="space-y-8"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card>
        <CardHeader>
          <CardTitle className="text-xs">
            {selectedSection ? selectedSection.charAt(0).toUpperCase() + selectedSection.slice(1) : "Select a section"}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {renderTable()}
        </CardContent>
      </Card>
    </motion.div>
  );
} 