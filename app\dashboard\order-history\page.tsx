"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { 
  Search, 
  Filter, 
  Download, 
  Eye, 
  Calendar,
  TrendingUp,
  TrendingDown,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  BarChart3,
  DollarSign,
  Percent,
  User,
  Mail,
  Phone,
  MapPin,
  Building,
  Activity,
  Target,
  Zap,
  Crown,
  Award
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { getApiUrl, getEndpointPath, secureFetch } from "../../config/api"
import secureStorageAPI from '@/app/lib/secureStorage'

interface Profile {
  username: string
  email: string
  name: string
  country: string
  phone_no: string
  address: string
}

interface Order {
  order_id: string
  challenge_type: string
  account_size: string
  platform: string
  created_at: string
  server?: string
  platform_login?: string
  status?: string
  profit_share?: number
}

interface OrdersData {
  pending: Order[]
  active: Order[]
  failed: Order[]
  stage2: Order[]
  live: Order[]
  total_count: number
}

interface ApiResponse {
  profile: Profile
  orders: OrdersData
}

interface OrderStats {
  totalOrders: number
  pendingOrders: number
  activeOrders: number
  failedOrders: number
  stage2Orders: number
  liveOrders: number
  totalAccountSize: number
  averageAccountSize: number
}

export default function OrderHistoryPage() {
  const [profile, setProfile] = useState<Profile | null>(null)
  const [orders, setOrders] = useState<OrdersData | null>(null)
  const [stats, setStats] = useState<OrderStats>({
    totalOrders: 0,
    pendingOrders: 0,
    activeOrders: 0,
    failedOrders: 0,
    stage2Orders: 0,
    liveOrders: 0,
    totalAccountSize: 0,
    averageAccountSize: 0
  })
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [platformFilter, setPlatformFilter] = useState<string>("all")
  const [dateFilter, setDateFilter] = useState<string>("all")

  useEffect(() => {
    fetchUserData()
  }, [])

  const fetchUserData = async () => {
    try {
      setIsLoading(true)
      const token = secureStorageAPI.getItem('access_token')
      
      if (!token) {
        console.error('No access token found')
        return
      }

      const response = await secureFetch('order/user/profile-and-orders')
      
      if (response.ok) {
        const data: ApiResponse = await response.json()
        setProfile(data.profile)
        setOrders(data.orders)
        calculateStats(data.orders)
      } else {
        console.error('Failed to fetch user data')
      }
    } catch (error) {
      console.error('Error fetching user data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const calculateStats = (ordersData: OrdersData) => {
    const allOrders = [
      ...ordersData.pending,
      ...ordersData.active,
      ...ordersData.failed,
      ...ordersData.stage2,
      ...ordersData.live
    ]
    
    const totalAccountSize = allOrders.reduce((sum, order) => sum + parseInt(order.account_size), 0)
    
    setStats({
      totalOrders: ordersData.total_count,
      pendingOrders: ordersData.pending.length,
      activeOrders: ordersData.active.length,
      failedOrders: ordersData.failed.length,
      stage2Orders: ordersData.stage2.length,
      liveOrders: ordersData.live.length,
      totalAccountSize,
      averageAccountSize: allOrders.length > 0 ? totalAccountSize / allOrders.length : 0
    })
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: "bg-yellow-500/15 text-yellow-400 border-yellow-500/25", icon: Clock, label: "Pending" },
      active: { color: "bg-blue-500/15 text-blue-400 border-blue-500/25", icon: Activity, label: "Active" },
      failed: { color: "bg-red-500/15 text-red-400 border-red-500/25", icon: XCircle, label: "Failed" },
      stage2: { color: "bg-purple-500/15 text-purple-400 border-purple-500/25", icon: Target, label: "Stage 2" },
      live: { color: "bg-green-500/15 text-green-400 border-green-500/25", icon: Crown, label: "Live" }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending
    const Icon = config.icon

    return (
      <Badge className={`${config.color} border`}>
        <Icon size={12} className="mr-1" />
        {config.label}
      </Badge>
    )
  }

  const getChallengeTypeBadge = (type: string) => {
    return (
      <Badge className="bg-orange-500/15 text-orange-400 border-orange-500/25 border">
        <Zap size={12} className="mr-1" />
        {type}
      </Badge>
    )
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getFilteredOrders = (orderList: Order[]) => {
    let filtered = orderList

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(order =>
        order.order_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.platform.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Platform filter
    if (platformFilter !== "all") {
      filtered = filtered.filter(order => order.platform === platformFilter)
    }

    // Date filter
    if (dateFilter !== "all") {
      const now = new Date()
      const filterDate = new Date()
      
      switch (dateFilter) {
        case "today":
          filterDate.setHours(0, 0, 0, 0)
          filtered = filtered.filter(order => new Date(order.created_at) >= filterDate)
          break
        case "week":
          filterDate.setDate(filterDate.getDate() - 7)
          filtered = filtered.filter(order => new Date(order.created_at) >= filterDate)
          break
        case "month":
          filterDate.setMonth(filterDate.getMonth() - 1)
          filtered = filtered.filter(order => new Date(order.created_at) >= filterDate)
          break
      }
    }

    return filtered
  }

  const uniquePlatforms = orders ? [...new Set([
    ...orders.pending.map(o => o.platform),
    ...orders.active.map(o => o.platform),
    ...orders.failed.map(o => o.platform),
    ...orders.stage2.map(o => o.platform),
    ...orders.live.map(o => o.platform)
  ])] : []

  if (isLoading) {
    return (
      <div className="p-6 flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-slate-400">Loading your trading data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen relative bg-gradient-to-br from-blue-950 via-slate-900 to-orange-950 text-white overflow-x-hidden">
      {/* Animated background shapes */}
      <div className="pointer-events-none select-none absolute inset-0 z-0">
        <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-500/20 to-orange-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-tr from-orange-500/20 to-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute top-1/2 left-1/2 w-[600px] h-[600px] bg-gradient-to-r from-blue-500/10 to-orange-500/10 rounded-full blur-3xl animate-pulse delay-500 -translate-x-1/2 -translate-y-1/2" />
      </div>
      
      <div className="container mx-auto px-4 py-8 md:py-16 relative z-10">
        <div className="max-w-7xl mx-auto space-y-8">
          {/* Profile Section */}
          {profile && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
              className="shadow-2xl rounded-3xl bg-gradient-to-br from-blue-900/80 via-slate-900/80 to-orange-900/80 border-2 border-blue-500/20 backdrop-blur-xl p-6"
            >
              <div className="flex flex-col md:flex-row items-start md:items-center gap-6">
                <Avatar className="w-20 h-20 border-4 border-blue-500/30">
                  <AvatarFallback className="bg-blue-600 text-white text-2xl font-bold">
                    {profile.name.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <h1 className="text-3xl font-bold text-slate-100 mb-2">{profile.name}</h1>
                  <p className="text-slate-400 mb-4">@{profile.username}</p>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div className="flex items-center gap-2 text-slate-300">
                      <Mail size={16} className="text-blue-400" />
                      <span className="text-sm">{profile.email}</span>
                    </div>
                    <div className="flex items-center gap-2 text-slate-300">
                      <Phone size={16} className="text-green-400" />
                      <span className="text-sm">{profile.phone_no}</span>
                    </div>
                    <div className="flex items-center gap-2 text-slate-300">
                      <MapPin size={16} className="text-orange-400" />
                      <span className="text-sm">{profile.country}</span>
                    </div>
                    <div className="flex items-center gap-2 text-slate-300">
                      <Building size={16} className="text-purple-400" />
                      <span className="text-sm">{profile.address}</span>
                    </div>
                  </div>
                </div>
            </div>
          </motion.div>
          )}

          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-slate-400 text-sm font-medium">Total Orders</p>
                    <p className="text-2xl font-bold text-slate-100">{stats.totalOrders}</p>
                  </div>
                  <div className="w-12 h-12 bg-blue-500/15 rounded-lg flex items-center justify-center">
                    <BarChart3 size={24} className="text-blue-400" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-slate-400 text-sm font-medium">Live Accounts</p>
                    <p className="text-2xl font-bold text-slate-100">{stats.liveOrders}</p>
                  </div>
                  <div className="w-12 h-12 bg-green-500/15 rounded-lg flex items-center justify-center">
                    <Crown size={24} className="text-green-400" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-slate-400 text-sm font-medium">Total Account Size</p>
                    <p className="text-2xl font-bold text-slate-100">{formatCurrency(stats.totalAccountSize)}</p>
                  </div>
                  <div className="w-12 h-12 bg-purple-500/15 rounded-lg flex items-center justify-center">
                    <DollarSign size={24} className="text-purple-400" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-slate-400 text-sm font-medium">Success Rate</p>
                    <p className="text-2xl font-bold text-slate-100">
                      {stats.totalOrders > 0 ? ((stats.liveOrders + stats.stage2Orders) / stats.totalOrders * 100).toFixed(1) : 0}%
                    </p>
                  </div>
                  <div className="w-12 h-12 bg-orange-500/15 rounded-lg flex items-center justify-center">
                    <Award size={24} className="text-orange-400" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Orders Section */}
          <div className="shadow-2xl rounded-3xl bg-gradient-to-br from-blue-900/80 via-slate-900/80 to-orange-900/80 border-2 border-blue-500/20 backdrop-blur-xl p-6">
          {/* Filters */}
            <div className="mb-6">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={16} />
                  <Input
                    placeholder="Search orders..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 bg-slate-700/50 border-slate-600 text-slate-100"
                  />
                </div>
                
                <Select value={platformFilter} onValueChange={setPlatformFilter}>
                  <SelectTrigger className="bg-slate-700/50 border-slate-600 text-slate-100">
                    <SelectValue placeholder="Platform" />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-700 border-slate-600">
                    <SelectItem value="all">All Platforms</SelectItem>
                    {uniquePlatforms.map(platform => (
                      <SelectItem key={platform} value={platform}>{platform}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={dateFilter} onValueChange={setDateFilter}>
                  <SelectTrigger className="bg-slate-700/50 border-slate-600 text-slate-100">
                    <SelectValue placeholder="Date Range" />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-700 border-slate-600">
                    <SelectItem value="all">All Time</SelectItem>
                    <SelectItem value="today">Today</SelectItem>
                    <SelectItem value="week">Last 7 Days</SelectItem>
                    <SelectItem value="month">Last 30 Days</SelectItem>
                  </SelectContent>
                </Select>

                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Download size={16} className="mr-2" />
                  Export Data
                </Button>
              </div>
            </div>

            {/* Orders Tabs */}
            {orders && (
              <Tabs defaultValue="all" className="w-full">
                <TabsList className="grid w-full grid-cols-6 bg-slate-800/50">
                  <TabsTrigger value="all" className="text-slate-300">All ({stats.totalOrders})</TabsTrigger>
                  <TabsTrigger value="pending" className="text-slate-300">Pending ({stats.pendingOrders})</TabsTrigger>
                  <TabsTrigger value="active" className="text-slate-300">Active ({stats.activeOrders})</TabsTrigger>
                  <TabsTrigger value="stage2" className="text-slate-300">Stage 2 ({stats.stage2Orders})</TabsTrigger>
                  <TabsTrigger value="live" className="text-slate-300">Live ({stats.liveOrders})</TabsTrigger>
                  <TabsTrigger value="failed" className="text-slate-300">Failed ({stats.failedOrders})</TabsTrigger>
                </TabsList>

                <TabsContent value="all" className="mt-6">
                  <OrdersTable orders={getFilteredOrders([
                    ...orders.pending,
                    ...orders.active,
                    ...orders.failed,
                    ...orders.stage2,
                    ...orders.live
                  ])} />
                </TabsContent>

                <TabsContent value="pending" className="mt-6">
                  <OrdersTable orders={getFilteredOrders(orders.pending)} />
                </TabsContent>

                <TabsContent value="active" className="mt-6">
                  <OrdersTable orders={getFilteredOrders(orders.active)} />
                </TabsContent>

                <TabsContent value="stage2" className="mt-6">
                  <OrdersTable orders={getFilteredOrders(orders.stage2)} />
                </TabsContent>

                <TabsContent value="live" className="mt-6">
                  <OrdersTable orders={getFilteredOrders(orders.live)} />
                </TabsContent>

                <TabsContent value="failed" className="mt-6">
                  <OrdersTable orders={getFilteredOrders(orders.failed)} />
                </TabsContent>
              </Tabs>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

// Orders Table Component
function OrdersTable({ orders }: { orders: Order[] }) {
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: "bg-yellow-500/15 text-yellow-400 border-yellow-500/25", icon: Clock, label: "Pending" },
      active: { color: "bg-blue-500/15 text-blue-400 border-blue-500/25", icon: Activity, label: "Active" },
      failed: { color: "bg-red-500/15 text-red-400 border-red-500/25", icon: XCircle, label: "Failed" },
      stage2: { color: "bg-purple-500/15 text-purple-400 border-purple-500/25", icon: Target, label: "Stage 2" },
      live: { color: "bg-green-500/15 text-green-400 border-green-500/25", icon: Crown, label: "Live" }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending
    const Icon = config.icon

    return (
      <Badge className={`${config.color} border`}>
        <Icon size={12} className="mr-1" />
        {config.label}
      </Badge>
    )
  }

  const getChallengeTypeBadge = (type: string) => {
    return (
      <Badge className="bg-orange-500/15 text-orange-400 border-orange-500/25 border">
        <Zap size={12} className="mr-1" />
        {type}
      </Badge>
    )
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="border-slate-700">
                      <TableHead className="text-slate-300">Order ID</TableHead>
            <TableHead className="text-slate-300">Challenge Type</TableHead>
            <TableHead className="text-slate-300">Account Size</TableHead>
            <TableHead className="text-slate-300">Platform</TableHead>
            <TableHead className="text-slate-300">Created At</TableHead>
                      <TableHead className="text-slate-300">Status</TableHead>
                      <TableHead className="text-slate-300">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
          {orders.length === 0 ? (
                      <TableRow>
              <TableCell colSpan={7} className="text-center py-8 text-slate-400">
                          No orders found matching your criteria
                        </TableCell>
                      </TableRow>
                    ) : (
            orders.map((order) => (
              <TableRow key={order.order_id} className="border-slate-700 hover:bg-slate-700/30">
                <TableCell className="text-slate-200 font-mono text-sm">{order.order_id}</TableCell>
                <TableCell>{getChallengeTypeBadge(order.challenge_type)}</TableCell>
                <TableCell className="text-slate-200 font-semibold">{formatCurrency(parseInt(order.account_size))}</TableCell>
                <TableCell className="text-slate-200">{order.platform}</TableCell>
                <TableCell className="text-slate-400 text-sm">
                  {formatDate(order.created_at)}
                          </TableCell>
                <TableCell>
                  {order.status ? getStatusBadge(order.status) : getStatusBadge('pending')}
                          </TableCell>
                          <TableCell>
                            <Button variant="ghost" size="sm" className="text-slate-400 hover:text-slate-200">
                              <Eye size={16} />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
    </div>
  )
} 