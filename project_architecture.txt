# Funded Horizon Project Architecture

## Overview
Funded Horizon is a trading platform that offers funded trading accounts to users. The platform includes user registration, authentication, trading challenges, account management, and an admin portal for managing users and orders.

## Tech Stack

### Frontend
- **Framework**: Next.js 14.2.16
- **Language**: TypeScript
- **Styling**: Tailwind CSS with shadcn/ui components
- **State Management**: React Hooks
- **Authentication**: JWT-based token authentication

### Backend
- Separate backend service hosted on Heroku
- API endpoints for user management, order processing, and trading data
- Integration with MyFXBook for trading account data

## Key Features

### User-Facing Features
1. **User Authentication**
   - Registration/Signup
   - Login
   - JWT token-based session management

2. **Dashboard**
   - Trading account statistics
   - Performance metrics (balance, equity, drawdown, profit)
   - Trade history visualization
   - Account status monitoring

3. **Trading Challenges**
   - Different challenge types (Standard, Stage Two, Live)
   - Various account sizes
   - Platform selection (MT4, MT5)
   - Payment processing

4. **Payment System**
   - Support for cryptocurrency payments (USDT-BEP20)
   - Payment verification with transaction IDs
   - Order confirmation

5. **Live Market Data**
   - Real-time market tickers
   - Market overview
   - Trading charts integration

### Admin Portal
1. **Admin Authentication**
   - Separate login system for administrators
   - Token-based access control

2. **User Management**
   - View all registered users
   - User details and information

3. **Order Management**
   - View all orders (running, completed, failed)
   - Process orders (approve, reject, mark as complete)
   - Pass/fail trading challenges
   - Manage stage progression (Standard → Stage Two → Live)

4. **Statistics and Analytics**
   - User growth metrics
   - Order completion rates
   - Revenue tracking
   - Performance analytics

## Project Structure

### Frontend Structure
- `/app` - Next.js App Router pages and layouts
  - `/dashboard` - User dashboard components
  - `/adminportal` - Admin portal components
  - `/login`, `/signup` - Authentication pages
- `/components` - Reusable UI components
- `/styles` - Global CSS and styling
- `/lib` - Utility functions and helpers
- `/public` - Static assets

### Key UI Components
1. **Navigation**
   - Main navbar for public pages
   - Dashboard sidebar for authenticated users
   - Admin navigation for administrators

2. **Dashboard Components**
   - Account statistics cards
   - Trading charts
   - Performance metrics
   - Order history tables

3. **Admin Components**
   - User tables
   - Order management interfaces
   - Statistics dashboards
   - Certificate management

4. **Authentication Forms**
   - Login form
   - Registration form with country selection
   - Profile management

## API Integration

### Backend Endpoints
1. **Authentication**
   - `/auth/signup` - User registration
   - `/auth/login` - User login
   - `/auth/users` - Get all users (admin only)

2. **Orders**
   - `/order/order` - Create new order
   - `/order/running_orders` - Get all running orders
   - `/order/completed_orders` - Get completed orders
   - `/order/complete_order/{id}` - Mark order as complete
   - `/order/pass_order/{id}` - Pass a trading challenge
   - `/order/fail_order/{id}` - Fail a trading challenge
   - `/order/edit_passed_order/{id}` - Edit passed order details
   - `/order/certificates` - Manage certificates
   - `/order/stats` - Get order statistics

3. **Trading Data**
   - `/myfxbook/fetch_account_details` - Get trading account details

### Data Flow
1. User registers and logs in
2. User purchases a trading challenge
3. Admin approves and processes the order
4. User completes the challenge
5. Admin reviews and passes/fails the challenge
6. If passed, user progresses to next stage (Stage Two or Live)

## Design System
- Dark theme for dashboard and admin portal
- Light theme for public pages
- Consistent color scheme with primary colors defined in Tailwind config
- Responsive design for mobile, tablet, and desktop
- Modern UI with cards, tables, and data visualization

## Deployment
- Frontend configured for standalone deployment
- Optimized for performance with caching headers
- Environment variables for API endpoints and configuration

## Important Implementation Details
1. **Authentication Flow**
   - JWT tokens stored in localStorage
   - Token verification on protected routes
   - Admin authentication with separate token system

2. **Form Validation**
   - Zod schema validation for all forms
   - Client-side validation before submission
   - Server response handling for errors

3. **Data Fetching**
   - API calls with proper error handling
   - Loading states for better UX
   - Data caching where appropriate

4. **Responsive Design**
   - Mobile-first approach
   - Breakpoints for different device sizes
   - Adaptive layouts for optimal viewing

5. **Performance Optimization**
   - Image optimization
   - Code splitting
   - Lazy loading of components
   - Efficient re-rendering with proper React patterns

## Third-Party Integrations
1. **Trading View** - For chart visualization
2. **MyFXBook** - For trading account data
3. **Payment Processors** - For handling cryptocurrency payments

This document provides a comprehensive overview of the Funded Horizon platform architecture, which can be used as a reference for building a similar trading platform.
