import { useState } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { User } from "./types"
import { BaseTable } from "./BaseTable"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Eye, EyeOff } from "lucide-react"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"

interface UsersTableProps {
  users: User[]
  searchTerm: string
  currentPage: number
  itemsPerPage: number
  onPageChange: (page: number) => void
  onSearch: (e: React.ChangeEvent<HTMLInputElement>) => void
  onRefresh: () => void
  isRefreshing: boolean
  onEditUser: (user: User, field: string) => void
}

export function UsersTable({
  users,
  searchTerm,
  currentPage,
  itemsPerPage,
  onPageChange,
  onSearch,
  onRefresh,
  isRefreshing,
  onEditUser
}: UsersTableProps) {
  const [showPassword, setShowPassword] = useState(false)
  const [viewUser, setViewUser] = useState<User | null>(null)

  const filteredUsers = users.filter(user =>
    Object.values(user).some(value =>
      value.toString().toLowerCase().includes(searchTerm.toLowerCase())
    )
  )

  const paginatedUsers = filteredUsers.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  )

  return (
    <>
      <BaseTable
        data={users}
        searchTerm={searchTerm}
        currentPage={currentPage}
        itemsPerPage={itemsPerPage}
        onPageChange={onPageChange}
        onSearch={onSearch}
        onRefresh={onRefresh}
        isRefreshing={isRefreshing}
        title="Users"
      >
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Username</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Country</TableHead>
                <TableHead>Phone</TableHead>
                <TableHead>Password</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>{user.id}</TableCell>
                  <TableCell>{user.username}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>{user.name}</TableCell>
                  <TableCell>{user.country}</TableCell>
                  <TableCell>{user.phone_no}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <span>
                        {showPassword ? user.hashed_password : '••••••••'}
                      </span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setViewUser(user)}
                    >
                      View Details
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </BaseTable>

      <Dialog open={!!viewUser} onOpenChange={() => setViewUser(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>User Details</DialogTitle>
          </DialogHeader>
          {viewUser && (
            <div className="space-y-4">
              {Object.entries(viewUser).map(([key, value]) => (
                <div key={key} className="grid grid-cols-2 gap-4">
                  <span className="font-medium capitalize">{key}</span>
                  <span>{value}</span>
                </div>
              ))}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  )
} 