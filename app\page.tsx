"use client"

import { useRef, useState, useEffect } from "react"
import { Footer } from "./component/components_footer"
import Testimonials from "./component/testimonials"
import Hero from "./components/hero-section"
import { AccountOptions } from "./components/account-options"
import { LiveMarketTicker } from "./components/live-market-ticker"
import { MarketOverview } from "./components/market-overview"
import StatisticsSection from "./component/StatisticsSection"
import { Advantages } from "./component/components_advantages"
import { CommunityCallToAction } from "./component/components_community-cta"
import { EconomicCalendar } from "./components/economic-calendar"
import { TradingInstruments } from "./components/trading-instruments"
import { TradingPlatforms } from "./components/trading-platforms"
import { RiskManagement } from "./components/risk-management"
import { PayoutSection } from "./components/PayoutSection"
import { EnhancedPayoutSection } from "./components/EnhancedPayoutSection"
import TradingChallenge from "./component/trading-challenge"
import FaqSection from "./component/faqsection"
import { SaleOfferModal } from './components/sale-offer-modal'
import { Navbar } from "./component/navbar"
import MovingSaleBanner from "./components/moving-sale-banner"
import EarningSection from "./components/earning-section"
import { ErrorBoundary } from "@/components/ui/error-boundary"

// Fallback component for when a component fails to load
const ComponentFallback = ({ componentName }: { componentName: string }) => (
  <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4 m-4">
    <p className="text-red-400 text-sm">
      Failed to load {componentName}. Please refresh the page.
    </p>
  </div>
)

// Optimized loading component for iPhone
const LoadingSpinner = () => (
  <div className="min-h-screen bg-[#0A0F1C] flex items-center justify-center">
    <div className="text-center">
      <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500 mx-auto mb-4"></div>
      <p className="text-white text-lg">Loading Funded Horizon...</p>
      <p className="text-gray-400 text-sm mt-2">Optimizing for your device</p>
    </div>
  </div>
)

export default function Home() {
  const faqRef = useRef<HTMLDivElement>(null)
  const tradingRef = useRef<HTMLDivElement>(null)
  const liveMarketRef = useRef<HTMLDivElement>(null)
  const [isClient, setIsClient] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    // Optimize for iPhone loading
    const initializeApp = () => {
      setIsClient(true)
      
      // Check if device is mobile
      const checkMobile = () => {
        const mobile = window.innerWidth < 768
        setIsMobile(mobile)
        
        // Add mobile-specific optimizations
        if (mobile) {
          // Reduce animation complexity on mobile
          document.documentElement.style.setProperty('--animation-duration', '1.5s')
          
          // Check if iOS Safari
          const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent)
          const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent)
          
          if (isIOS && isSafari) {
            // Add hardware acceleration for iOS Safari
            document.body.style.webkitTransform = 'translateZ(0)'
            document.body.style.transform = 'translateZ(0)'
          }
        }
      }
      
      checkMobile()
      window.addEventListener('resize', checkMobile)
      
      return () => window.removeEventListener('resize', checkMobile)
    }

    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initializeApp)
    } else {
      initializeApp()
    }
  }, [])

  const scrollToSection = (ref: React.RefObject<HTMLDivElement>) => {
    ref.current?.scrollIntoView({ behavior: 'smooth' })
  }

  // Show loading state until client-side hydration is complete
  if (!isClient) {
    return <LoadingSpinner />
  }

  return (
    <>
      <ErrorBoundary fallback={<ComponentFallback componentName="Sale Offer Modal" />}>
        <SaleOfferModal />
      </ErrorBoundary>
      
      <main className={`bg-[#0A0F1C] ${typeof window !== 'undefined' && /iPhone|iPad|iPod/.test(navigator.userAgent) ? 'min-h-screen-ios' : 'min-h-screen'}`}>
        <ErrorBoundary fallback={<ComponentFallback componentName="Moving Sale Banner" />}>
          <MovingSaleBanner />
        </ErrorBoundary>
        
        <ErrorBoundary fallback={<ComponentFallback componentName="Navbar" />}>
          <Navbar />
        </ErrorBoundary>
        
        <ErrorBoundary fallback={<ComponentFallback componentName="Hero Section" />}>
          <Hero />
        </ErrorBoundary>
        
        <ErrorBoundary fallback={<ComponentFallback componentName="Live Market Ticker" />}>
          <div id="live-market" className="block w-full">
            <LiveMarketTicker />
          </div>
        </ErrorBoundary>
        
        <ErrorBoundary fallback={<ComponentFallback componentName="Statistics Section" />}>
          <StatisticsSection />
        </ErrorBoundary>
        
        <ErrorBoundary fallback={<ComponentFallback componentName="Enhanced Payout Section" />}>
          <EnhancedPayoutSection />
        </ErrorBoundary>
        
        <ErrorBoundary fallback={<ComponentFallback componentName="Market Overview" />}>
          <MarketOverview />
        </ErrorBoundary>
        
        <ErrorBoundary fallback={<ComponentFallback componentName="Trading Challenge" />}>
          <div id="pricing">
            <TradingChallenge />
          </div>
        </ErrorBoundary>
        
        <ErrorBoundary fallback={<ComponentFallback componentName="Risk Management" />}>
          <RiskManagement />
        </ErrorBoundary>
        
        <ErrorBoundary fallback={<ComponentFallback componentName="Community Call to Action" />}>
          <CommunityCallToAction />
        </ErrorBoundary>
        
        <ErrorBoundary fallback={<ComponentFallback componentName="Earning Section" />}>
          <EarningSection />
        </ErrorBoundary>
        
        <ErrorBoundary fallback={<ComponentFallback componentName="FAQ Section" />}>
          <div id="faqs">
            <FaqSection/>
          </div>
        </ErrorBoundary>
        
        <ErrorBoundary fallback={<ComponentFallback componentName="Footer" />}>
          <Footer />
        </ErrorBoundary>
      </main>
    </>
  )
}