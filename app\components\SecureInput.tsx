'use client';

import React, { useState, useEffect, forwardRef } from 'react';
import { InputValidator } from '@/lib/security';

interface SecureInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  validation?: {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    customValidation?: (value: string) => { isValid: boolean; error?: string };
  };
  sanitize?: boolean;
  showPasswordToggle?: boolean;
  icon?: React.ReactNode;
  className?: string;
  onValidationChange?: (isValid: boolean, errors: string[]) => void;
}

const SecureInput = forwardRef<HTMLInputElement, SecureInputProps>(
  ({ 
    label, 
    error: externalError, 
    validation = {}, 
    sanitize = true, 
    showPasswordToggle = false,
    icon,
    className = '',
    onValidationChange,
    onChange,
    onBlur,
    value,
    ...props 
  }, ref) => {
    const [internalValue, setInternalValue] = useState(value || '');
    const [internalError, setInternalError] = useState<string>('');
    const [showPassword, setShowPassword] = useState(false);
    const [isValid, setIsValid] = useState(true);
    const [validationErrors, setValidationErrors] = useState<string[]>([]);

    // Update internal value when external value changes
    useEffect(() => {
      setInternalValue(value || '');
    }, [value]);

    const validateInput = (inputValue: string): { isValid: boolean; errors: string[] } => {
      const errors: string[] = [];

      // Required validation
      if (validation.required && !inputValue.trim()) {
        errors.push('This field is required');
      }

      // Min length validation
      if (validation.minLength && inputValue.length < validation.minLength) {
        errors.push(`Minimum length is ${validation.minLength} characters`);
      }

      // Max length validation
      if (validation.maxLength && inputValue.length > validation.maxLength) {
        errors.push(`Maximum length is ${validation.maxLength} characters`);
      }

      // Pattern validation
      if (validation.pattern && inputValue && !validation.pattern.test(inputValue)) {
        errors.push('Invalid format');
      }

      // Custom validation
      if (validation.customValidation && inputValue) {
        const customResult = validation.customValidation(inputValue);
        if (!customResult.isValid && customResult.error) {
          errors.push(customResult.error);
        }
      }

      // Email validation (if type is email)
      if (props.type === 'email' && inputValue && !InputValidator.validateEmail(inputValue)) {
        errors.push('Please enter a valid email address');
      }

      // Password validation (if type is password)
      if (props.type === 'password' && inputValue) {
        const passwordValidation = InputValidator.validatePassword(inputValue);
        if (!passwordValidation.isValid) {
          errors.push(...passwordValidation.errors);
        }
      }

      return { isValid: errors.length === 0, errors };
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      let newValue = e.target.value;

      // Sanitize input if enabled
      if (sanitize) {
        newValue = InputValidator.sanitizeString(newValue);
      }

      // Validate input
      const validationResult = validateInput(newValue);
      setIsValid(validationResult.isValid);
      setValidationErrors(validationResult.errors);
      setInternalError(validationResult.errors[0] || '');

      // Update internal value
      setInternalValue(newValue);

      // Call parent onChange with sanitized value
      if (onChange) {
        const sanitizedEvent = {
          ...e,
          target: {
            ...e.target,
            value: newValue,
            name: props.name
          }
        };
        onChange(sanitizedEvent as React.ChangeEvent<HTMLInputElement>);
      }

      // Notify parent of validation status
      if (onValidationChange) {
        onValidationChange(validationResult.isValid, validationResult.errors);
      }
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      // Re-validate on blur
      const validationResult = validateInput(e.target.value);
      setIsValid(validationResult.isValid);
      setValidationErrors(validationResult.errors);
      setInternalError(validationResult.errors[0] || '');

      if (onBlur) {
        onBlur(e);
      }
    };

    const togglePasswordVisibility = () => {
      setShowPassword(!showPassword);
    };

    const finalError = externalError || internalError;
    const inputType = showPasswordToggle && props.type === 'password' 
      ? (showPassword ? 'text' : 'password') 
      : props.type;

    return (
      <div className="flex flex-col gap-2">
        {label && (
          <label className="text-sm font-semibold text-gray-200 tracking-wide">
            {label}
            {validation.required && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}
        
        <div className="relative">
          {icon && (
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
              {icon}
            </div>
          )}
          
          <input
            ref={ref}
            {...props}
            type={inputType}
            value={internalValue}
            onChange={handleChange}
            onBlur={handleBlur}
            className={`
              w-full px-4 py-3.5 rounded-xl bg-gray-900/50 text-white border 
              ${finalError ? 'border-red-500' : 'border-gray-700'} 
              focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 
              transition-all duration-300 placeholder-gray-500
              ${icon ? 'pl-12' : 'pl-4'}
              ${showPasswordToggle ? 'pr-12' : ''}
              ${className}
            `}
          />
          
          {showPasswordToggle && props.type === 'password' && (
            <button
              type="button"
              onClick={togglePasswordVisibility}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300 transition-colors"
            >
              {showPassword ? (
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                  <line x1="1" y1="1" x2="23" y2="23"/>
                </svg>
              ) : (
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                  <circle cx="12" cy="12" r="3"/>
                </svg>
              )}
            </button>
          )}
        </div>
        
        {finalError && (
          <div className="flex items-center gap-2 text-red-400 text-sm">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <circle cx="12" cy="12" r="10"/>
              <line x1="15" y1="9" x2="9" y2="15"/>
              <line x1="9" y1="9" x2="15" y2="15"/>
            </svg>
            {finalError}
          </div>
        )}
        
        {validationErrors.length > 1 && (
          <div className="text-red-400 text-sm">
            <ul className="list-disc list-inside space-y-1">
              {validationErrors.slice(1).map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </div>
        )}
      </div>
    );
  }
);

SecureInput.displayName = 'SecureInput';

export default SecureInput; 