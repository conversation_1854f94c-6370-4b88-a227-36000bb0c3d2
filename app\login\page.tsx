"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"

export default function LoginPage() {
  const router = useRouter()
  useEffect(() => {
    // Optionally, redirect to home or admin login
    // router.push("/")
  }, [router])
  return (
    <div className="min-h-screen flex items-center justify-center bg-[#0a1929]">
      <div className="bg-[#0d2339]/90 p-8 rounded-xl border border-gray-800/50 w-full max-w-md text-center">
        <h1 className="text-2xl font-bold text-orange-400 mb-4">Login Disabled</h1>
        <p className="text-white">This login page is no longer available.<br />If you are an admin, please use the <b>Admin Portal Login</b> at <code>/adminportol/login</code>.</p>
      </div>
    </div>
  )
}
