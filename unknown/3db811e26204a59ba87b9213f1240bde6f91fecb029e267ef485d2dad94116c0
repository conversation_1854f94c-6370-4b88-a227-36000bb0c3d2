import { useState, useEffect, useRef } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Search, XCircle, CreditCard, Hash, Shield, LineChart, ChevronDown, User as UserIcon, Wallet, AlertTriangle, MoreVertical, Eye, EyeOff, Receipt, Refresh<PERSON><PERSON>, Co<PERSON>, Check } from "lucide-react" 
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { EditOrderModal } from "@/components/edit-order-modal"
import { OrderStatus, OrderType, RejectReasons, FailReasons } from "@/types/order"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { ViewOrderModal } from "@/components/view-order-modal"
import { FailOrderModal } from "@/components/fail-order-modal"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { PassOrderModal } from "@/components/pass-order-modal"
import { PaymentProofModal } from "@/components/payment-proof-modal"
import { toast } from "@/components/ui/use-toast"
import { CertificateTable } from "@/components/certificate-table"
import { UsersTable } from "./admin/UsersTable"
import { RunningOrdersTable } from "./admin/RunningOrdersTable"
import { Pagination } from "./admin/Pagination"
import { AdminTablesProps, User, RunningOrder, OrderDetails } from "./admin/types"
import { OrdersTable } from "./admin/OrdersTable"
import { CompletedOrdersTable } from "./admin/CompletedOrdersTable"
import { FailedOrdersTable } from "./admin/FailedOrdersTable"
import { PassedOrdersTable } from "./admin/PassedOrdersTable"
import { StageTwoOrdersTable } from "./admin/StageTwoOrdersTable"
import { LiveOrdersTable } from "./admin/LiveOrdersTable"
import { getApiUrl, getEndpointPath } from "../app/config/api"

const mockOrders: OrderDetails[] = [
  {
    id: 1,
    user: {
      name: "John Doe",
      email: "<EMAIL>",
      hashed_password: "+**********",
    },
    amount: "$1000",
    status: OrderStatus.COMPLETED,
    type: OrderType.STANDARD,
    createdAt: "2023-04-05",
    accountType: "Challenge Phase-1",
    platformType: "MT4",
    platformLogin: "SAA",
    platformPassword: "password123",
    server: "None",
    startingBalance: 5003,
    currentBalance: 5000,
    profitTarget: 400,
    paymentProof: "https://example.com/proof1.jpg",
    paymentMethod: "Credit Card",
    txid: "TXN123456789"
  },
  {
    id: 2,
    user: {
      name: "Jane Smith", 
      email: "<EMAIL>",
      hashed_password: "+**********",
    },
    amount: "$1500",
    status: OrderStatus.PENDING,
    type: OrderType.STANDARD,
    createdAt: "2023-04-10",
    accountType: "Challenge Phase-2",
    platformType: "MT5",
    platformLogin: "SAB",
    platformPassword: "password456",
    server: "None",
    startingBalance: 10000,
    currentBalance: 11000,
    profitTarget: 800,
    paymentProof: "https://example.com/proof2.jpg",
    paymentMethod: "Bank Transfer",
    txid: "TXN987654321"
  },
]

export function AdminTables({ selectedSection }: AdminTablesProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [orders, setOrders] = useState(mockOrders)
  const [completedOrders, setCompletedOrders] = useState<OrderDetails[]>([])
  const [failedOrders, setFailedOrders] = useState<OrderDetails[]>([])
  const [passOrders, setPassOrders] = useState<OrderDetails[]>([])
  const [stageTwoOrders, setStageTwoOrders] = useState<OrderDetails[]>([])
  const [liveOrders, setLiveOrders] = useState<OrderDetails[]>([])
  const [runningOrders, setRunningOrders] = useState<RunningOrder[]>([])
  const [users, setUsers] = useState<User[]>([])
  const [editedOrder, setEditedOrder] = useState<OrderDetails | null>(null)
  const [localOrderChanges, setLocalOrderChanges] = useState<{ [key: string]: OrderDetails }>({})
  const [isMobile, setIsMobile] = useState(false)
  const [viewOrder, setViewOrder] = useState<OrderDetails | null>(null); // State for viewing order details
  const [viewUser, setViewUser] = useState<User | null>(null); // State for viewing user details
  const [isClient, setIsClient] = useState(false)
  const [selectedFailureReason, setSelectedFailureReason] = useState<FailReasons | null>(null)
  const [failureModalOrder, setFailureModalOrder] = useState<string | null>(null)
  const [failureDate, setFailureDate] = useState<string>(new Date().toISOString().split('T')[0])
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)
  const [totalPages, setTotalPages] = useState(1)

  // New state variables
  const [editUserModalOpen, setEditUserModalOpen] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [editField, setEditField] = useState<string>("")
  const [editValue, setEditValue] = useState<string>("")

  // Add a new state for password
  const [showPassword, setShowPassword] = useState(false)

  // Add a new state for showing payment proof
  const [showPaymentProof, setShowPaymentProof] = useState<OrderDetails | null>(null)

  // Add refresh function
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Add a new state for copied txid
  const [copiedTxid, setCopiedTxid] = useState<string | null>(null)

  // Function to handle page change
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage)
    }
  }

  // Function to handle search input
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    // Reset to page 1 when search term changes
    setCurrentPage(1);
  };

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  useEffect(() => {
    fetch('/api/proxy/auth/users')
      .then((response) => response.json())
      .then((data) => {
        console.log('Users data received:', data);
        const formattedUsers = data.map((user: any) => ({
          id: user.id,
          username: user.username,
          email: user.email,
          name: user.name,
          country: user.country,
          phone_no: user.phone_no,
          address: user.address,
          hashed_password: user.hashed_password,
          createdAt: new Date().toISOString().split("T")[0],
        }))
        console.log('Formatted users:', formattedUsers);
        // Sort users by ID in descending order (newest first)
        const sortedUsers = formattedUsers.sort((a: User, b: User) => b.id - a.id);
        setUsers(sortedUsers);
      })
      .catch((error) => console.error("Error fetching users:", error))

    // Fetch running orders
    fetch('/api/proxy/ordors/running_orders')
      .then((response) => response.json())
      .then((data) => {
        console.log('Running orders data received:', data);
        // Reverse the array to show newest first
        setRunningOrders([...data].reverse());
      })
      .catch((error) => console.error("Error fetching running orders:", error))

    // Fetch completed orders
    fetch('/api/proxy/ordors/completed_orders')
      .then((response) => response.json())
      .then((data) => {
        console.log('Completed orders data received:', data);
        const formattedCompletedOrders = data.map((order: any) => ({
          id: order.complete_order_id,
          type: order.type as OrderType || OrderType.STANDARD,
          order_id: order.order_id,
          user: {
            name: order.username,
            email: order.email,
            password: order.password,
          },
          amount: order.account_size,
          status: OrderStatus.COMPLETED,
          createdAt: order.image?.created_at 
            ? new Date(order.image.created_at).toLocaleString()
            : order.created_at 
              ? new Date(order.created_at).toLocaleString()
              : new Date().toLocaleString(),
          accountType: order.challenge_type,
          platformType: order.platform,
          platformLogin: order.platform_login,
          platformPassword: order.platform_password,
          server: order.server,
          sessionId: order.session_id,
          terminalId: order.terminal_id?.toString(),
          startingBalance: parseInt(order.account_size),
          currentBalance: parseInt(order.account_size),
          profitTarget: order.profit_target,
          paymentMethod: order.payment_method,
          txid: order.txid,
          image: order.image
        }))
        console.log('Formatted completed orders:', formattedCompletedOrders);
        // Reverse the array to show newest first
        setCompletedOrders([...formattedCompletedOrders].reverse());
      })
      .catch((error) => console.error("Error fetching completed orders:", error))

    // Fetch pending orders
    fetch('/api/proxy/ordors/orders')
      .then((response) => response.json())
      .then((data) => {
        console.log('Orders data received:', data);
        const formattedOrders = data.map((order: any) => ({
          id: order.id,
          type: order.type as OrderType || OrderType.STANDARD,
          user: {
            name: order.username,
            email: order.email,
            password: order.password,
          },
          amount: order.account_size,
          status: OrderStatus.PENDING,
          createdAt: order.image?.created_at 
            ? new Date(order.image.created_at).toLocaleString()
            : order.created_at 
              ? new Date(order.created_at).toLocaleString()
              : new Date().toLocaleString(),
          accountType: order.challenge_type,
          platformType: order.platform,
          platformLogin: order.txid,
          platformPassword: "N/A",
          server: "None",
          startingBalance: parseInt(order.account_size),
          currentBalance: parseInt(order.account_size),
          profitTarget: 0,
          paymentMethod: order.payment_method,
          txid: order.txid,
          order_id: order.id,
          image: order.image
        }))

        // Filter out orders that are in completedOrders
        const pendingOrders = formattedOrders.filter(
          (order: OrderDetails) => !completedOrders.some((completedOrder) => 
            completedOrder.order_id && order.id && completedOrder.order_id === order.id.toString()
          )
        )
        console.log('Formatted pending orders:', pendingOrders);
        // Reverse the array to show newest first
        setOrders([...pendingOrders].reverse());
      })
      .catch((error) => console.error("Error fetching orders:", error))

    // Fetch failed orders
    fetch('/api/proxy/ordors/failed_orders')
      .then((response) => response.json())
      .then((data) => {
        console.log('Failed orders data received:', data);
        const formattedFailedOrders = data.map((order: any) => ({
          id: order.fail_order_id,
          type: order.type as OrderType || OrderType.STANDARD,
          order_id: order.order_id,
          user: {
            name: order.username,
            email: order.email,
          },
          amount: order.account_size,
          status: OrderStatus.CANCELLED,
          createdAt: order.date || new Date().toISOString().split("T")[0],
          accountType: order.challenge_type,
          platformType: order.platform,
          paymentMethod: order.payment_method,
          txid: order.txid,
          reason: order.reason,
          server: order.server,
          platformLogin: order.platform_login,
          platformPassword: order.platform_password
        }));
        console.log('Formatted failed orders:', formattedFailedOrders);
        // Reverse the array to show newest first
        setFailedOrders([...formattedFailedOrders].reverse());
      })
      .catch((error) => console.error("Error fetching failed orders:", error))

    // Fetch passed orders
    fetch('/api/proxy/ordors/passed_orders')
      .then((response) => response.json())
      .then((data) => {
        console.log('Passed orders data received:', data);
        const formattedPassOrders = data.map((order: any) => ({
          id: order.pass_order_id,
          type: OrderType.STANDARD,
          order_id: order.order_id,
          user: {
            name: order.username,
            email: order.email,
          },
          amount: order.account_size,
          status: "Passed",
          createdAt: order.pass_date ? new Date(order.pass_date).toISOString().split("T")[0] : new Date().toISOString().split("T")[0],
          accountType: order.challenge_type,
          platformType: order.platform,
          profitTarget: order.profit_amount,
          notes: order.notes,
          txid: order.order_id
        }));
        console.log('Formatted passed orders:', formattedPassOrders);
        setPassOrders([...formattedPassOrders].reverse());
      })
      .catch((error) => console.error("Error fetching passed orders:", error));

    // Fetch stage two orders
    fetch('/api/proxy/ordors/stage_two_orders')
      .then((response) => response.json())
      .then((data) => {
        console.log('Stage two orders data received:', data);
        const formattedStageTwoOrders = data.map((order: any) => ({
          id: order.id,
          type: OrderType.STANDARD,
          order_id: order.order_id,
          user: {
            name: order.username,
            email: order.email,
          },
          amount: order.account_size,
          status: "Stage Two",
          createdAt: order.created_at ? new Date(order.created_at).toISOString().split("T")[0] : new Date().toISOString().split("T")[0],
          accountType: order.challenge_type,
          platformType: order.platform,
          platformLogin: order.platform_login,
          platformPassword: order.platform_password,
          server: order.server,
          sessionId: order.session_id,
          terminalId: order.terminal_id,
          profitTarget: order.profit_target,
          txid: order.order_id
        }));
        console.log('Formatted stage two orders:', formattedStageTwoOrders);
        setStageTwoOrders([...formattedStageTwoOrders].reverse());
      })
      .catch((error) => console.error("Error fetching stage two orders:", error));

    // Fetch live orders
    fetch('/api/proxy/ordors/live_orders')
      .then((response) => response.json())
      .then((data) => {
        console.log('Live orders data received:', data);
        const formattedLiveOrders = data.map((order: any) => ({
          id: order.id || 0,
          type: OrderType.STANDARD,
          order_id: order.order_id,
          user: {
            name: order.username || "N/A",
            email: order.email || "N/A",
          },
          amount: order.account_size || "0",
          status: "Live",
          createdAt: order.created_at ? new Date(order.created_at).toISOString().split("T")[0] : new Date().toISOString().split("T")[0],
          accountType: order.challenge_type || "N/A",
          platformType: order.platform || "N/A",
          platformLogin: order.platform_login || "N/A",
          platformPassword: order.platform_password || "N/A",
          server: order.server || "N/A",
          sessionId: order.session_id || "N/A",
          terminalId: order.terminal_id || "N/A",
          txid: order.order_id
        }));
        console.log('Formatted live orders:', formattedLiveOrders);
        setLiveOrders([...formattedLiveOrders].reverse());
      })
      .catch((error) => console.error("Error fetching live orders:", error));

  }, [])

  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    // Calculate total pages based on current section data
    let totalItems = 0;
    
    switch (selectedSection) {
      case "users":
        totalItems = users.length;
        break;
      case "completedOrders":
        totalItems = completedOrders.length;
        break;
      case "failedOrders":
        totalItems = failedOrders.length;
        break;
      case "passOrders":
        totalItems = passOrders.length;
        break;
      case "stageTwoOrders":
        totalItems = stageTwoOrders.length;
        break;
      case "liveOrders":
        totalItems = liveOrders.length;
        break;
      case "runningOrders":
        totalItems = runningOrders.length;
        break;
      case "orders":
      default:
        totalItems = orders.length;
        break;
    }
    
    setTotalPages(Math.ceil(totalItems / itemsPerPage));
    // Reset to first page when changing sections or when filtered results change
    setCurrentPage(1);
  }, [selectedSection, itemsPerPage, users.length, completedOrders.length, failedOrders.length, passOrders.length, stageTwoOrders.length, liveOrders.length, runningOrders.length, searchTerm]);

  const handleEditOrder = (order: OrderDetails) => {
    setEditedOrder(order)
  }

  const handleViewOrder = (order: OrderDetails) => {
    setViewOrder(order);
  }

  const handleViewUser = (user: User) => {
    setViewUser(user);
  }

  const handleSaveChanges = async (order: OrderDetails): Promise<void> => {
    console.log("Saving changes locally for order:", order.id);
    console.log("Changes:", order);
    
    // Store the edited order in localOrderChanges using the order ID as key
    setLocalOrderChanges(prev => ({
      ...prev,
      [order.id]: order
    }));
    setEditedOrder(null);
  };

  // Function to refresh all data
  const refreshData = async () => {
    setIsRefreshing(true);
    try {
      // Fetch users
      const usersResponse = await fetch('/api/proxy/auth/users');
      const usersData = await usersResponse.json();
      const formattedUsers = usersData.map((user: any) => ({
        id: user.id,
        username: user.username,
        email: user.email,
        name: user.name,
        country: user.country,
        phone_no: user.phone_no,
        address: user.address,
        hashed_password: user.hashed_password,
        createdAt: new Date().toISOString().split("T")[0],
      }));
      setUsers([...formattedUsers].sort((a: User, b: User) => b.id - a.id));

      // Fetch running orders
      const runningOrdersResponse = await fetch('/api/proxy/ordors/running_orders');
      const runningOrdersData = await runningOrdersResponse.json();
      setRunningOrders([...runningOrdersData].reverse());

      // Fetch failed orders
      const failedOrdersResponse = await fetch('/api/proxy/ordors/failed_orders');
      const failedOrdersData = await failedOrdersResponse.json();
      const formattedFailedOrders = failedOrdersData.map((order: any) => ({
        id: order.fail_order_id,
        type: order.type as OrderType || OrderType.STANDARD,
        order_id: order.order_id,
        user: {
          name: order.username,
          email: order.email,
        },
        amount: order.account_size,
        status: OrderStatus.CANCELLED,
        createdAt: order.date || new Date().toISOString().split("T")[0],
        accountType: order.challenge_type,
        platformType: order.platform,
        paymentMethod: order.payment_method,
        txid: order.txid,
        reason: order.reason,
        server: order.server,
        platformLogin: order.platform_login,
        platformPassword: order.platform_password
      }));
      setFailedOrders([...formattedFailedOrders].reverse());

      // Fetch passed orders
      const passedOrdersResponse = await fetch('/api/proxy/ordors/passed_orders');
      const passedOrdersData = await passedOrdersResponse.json();
      const formattedPassOrders = passedOrdersData.map((order: any) => ({
        id: order.pass_order_id,
        type: OrderType.STANDARD,
        order_id: order.order_id,
        user: {
          name: order.username,
          email: order.email,
        },
        amount: order.account_size,
        status: "Passed",
        createdAt: order.pass_date ? new Date(order.pass_date).toISOString().split("T")[0] : new Date().toISOString().split("T")[0],
        accountType: order.challenge_type,
        platformType: order.platform,
        profitTarget: order.profit_amount,
        notes: order.notes,
        txid: order.order_id
      }));
      setPassOrders([...formattedPassOrders].reverse());

      // Fetch stage two orders
      const stageTwoOrdersResponse = await fetch('/api/proxy/ordors/stage_two_orders');
      const stageTwoOrdersData = await stageTwoOrdersResponse.json();
      const formattedStageTwoOrders = stageTwoOrdersData.map((order: any) => ({
        id: order.id,
        type: OrderType.STANDARD,
        order_id: order.order_id,
        user: {
          name: order.username,
          email: order.email,
        },
        amount: order.account_size,
        status: "Stage Two",
        createdAt: order.created_at ? new Date(order.created_at).toISOString().split("T")[0] : new Date().toISOString().split("T")[0],
        accountType: order.challenge_type,
        platformType: order.platform,
        platformLogin: order.platform_login,
        platformPassword: order.platform_password,
        server: order.server,
        sessionId: order.session_id,
        terminalId: order.terminal_id,
        profitTarget: order.profit_target,
        txid: order.order_id
      }));
      setStageTwoOrders([...formattedStageTwoOrders].reverse());

      // Fetch live orders
      const liveOrdersResponse = await fetch('/api/proxy/ordors/live_orders');
      const liveOrdersData = await liveOrdersResponse.json();
      const formattedLiveOrders = liveOrdersData.map((order: any) => ({
        id: order.id || 0,
        type: OrderType.STANDARD,
        order_id: order.order_id,
        user: {
          name: order.username || "N/A",
          email: order.email || "N/A",
        },
        amount: order.account_size || "0",
        status: "Live",
        createdAt: order.created_at ? new Date(order.created_at).toISOString().split("T")[0] : new Date().toISOString().split("T")[0],
        accountType: order.challenge_type || "N/A",
        platformType: order.platform || "N/A",
        platformLogin: order.platform_login || "N/A",
        platformPassword: order.platform_password || "N/A",
        server: order.server || "N/A",
        sessionId: order.session_id || "N/A",
        terminalId: order.terminal_id || "N/A",
        txid: order.order_id
      }));
      setLiveOrders([...formattedLiveOrders].reverse());

    } catch (error) {
      console.error("Error refreshing data:", error);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Update handleConfirmOrder to use alert instead of toast
  const handleConfirmOrder = async (order: OrderDetails) => {
    try {
      const orderToConfirm = localOrderChanges[order.id] || order;
      console.log("=== Starting order confirmation ===");
      console.log("Order ID:", orderToConfirm.id);
      console.log("Order Status:", orderToConfirm.status);
      console.log("Account Type:", orderToConfirm.accountType);
      console.log("Selected Section:", selectedSection);
      
      const formData = new FormData();
      formData.append('server', orderToConfirm.server || '');
      formData.append('platform_login', orderToConfirm.platformLogin || '');
      formData.append('platform_password', orderToConfirm.platformPassword || '');
      formData.append('session_id', orderToConfirm.sessionId || '');
      formData.append('terminal_id', orderToConfirm.terminalId?.toString() || '');
      formData.append('profit_target', orderToConfirm.profitTarget?.toString() || '0');

      let response;
      let apiAccountType = '';
      const orderId = orderToConfirm.order_id ? 
                     orderToConfirm.order_id.toString().replace(/^(FDH|FxE)/i, '').replace(/\D/g, '') : 
                     orderToConfirm.id ? 
                     orderToConfirm.id.toString().replace(/^(FDH|FxE)/i, '').replace(/\D/g, '') : 
                     '';

      if (selectedSection === "passOrders") {
        apiAccountType = orderToConfirm.accountType === "Stage Two" ? "stage2" : 
                        orderToConfirm.accountType === "Live" ? "live" :
                        orderToConfirm.status === "Stage Two" || orderToConfirm.status === "STAGE_TWO" ? "stage2" : 
                        orderToConfirm.status === "Live" || orderToConfirm.status === "LIVE" ? "live" : "stage2";
        
        formData.append('account_type', apiAccountType);
        
        response = await fetch('/api/proxy/ordors/edit_passed_order/' + orderId, {
          method: 'PUT',
          body: formData
        });
      } else if (orderToConfirm.status === "Stage Two" || orderToConfirm.status === "STAGE_TWO" || orderToConfirm.status === "Live" || orderToConfirm.status === "LIVE") {
        apiAccountType = orderToConfirm.status === "Stage Two" || orderToConfirm.status === "STAGE_TWO" ? "stage2" : "live";
        formData.append('account_type', apiAccountType);
        
        response = await fetch('/api/proxy/ordors/edit_passed_order/' + orderId, {
          method: 'PUT',
          body: formData
        });
      } else {
        response = await fetch('/api/proxy/ordors/complete_order/' + orderId, {
          method: 'POST',
          body: formData
        });
      }

      if (response.ok) {
        // Update the appropriate orders list based on the current section
        if (selectedSection === "passOrders") {
          const updatedOrders = passOrders.map(o => {
            if (o.id === orderToConfirm.id) {
              return { ...o, ...orderToConfirm };
            }
            return o;
          });
          setPassOrders(updatedOrders);
          alert(`Order ${orderId} has been updated to ${apiAccountType === "stage2" ? "Stage Two" : "Live"} successfully!`);
        } else if (orderToConfirm.status === "Stage Two" || orderToConfirm.status === "Live") {
          const updatedOrders = stageTwoOrders.map(o => {
            if (o.id === orderToConfirm.id) {
              return { ...o, ...orderToConfirm };
            }
            return o;
          });
          setStageTwoOrders(updatedOrders);
          alert(`Order ${orderId} has been updated to ${apiAccountType === "stage2" ? "Stage Two" : "Live"} successfully!`);
        } else {
          const updatedOrders = orders.filter(o => o.id !== orderToConfirm.id);
          setOrders(updatedOrders);
          
          const completedOrder = {
            ...orderToConfirm,
            status: OrderStatus.COMPLETED
          };
          setCompletedOrders(prevOrders => [completedOrder, ...prevOrders]);
          alert(`Order ${orderId} has been completed successfully!`);
        }

        // Clear the local changes for this order
        setLocalOrderChanges(prev => {
          const newChanges = { ...prev };
          delete newChanges[orderToConfirm.id];
          return newChanges;
        });
      } else {
        const errorData = await response.json();
        console.error('API Error Response:', errorData);
        alert(`Failed to ${selectedSection === "passOrders" || orderToConfirm.status === "Stage Two" || orderToConfirm.status === "Live" ? 
          `update order ${orderId} to ${apiAccountType === "stage2" ? "Stage Two" : "Live"}` : 
          `complete order ${orderId}`}. ${errorData.message || ""}`);
      }
    } catch (error) {
      console.error('Error completing order:', error);
      alert('Failed to process order. Please try again.');
    }
  };

  const handlePassOrder = async (orderId: string, profitAmount: number, notes: string) => {
    try {
      // Extract numeric part from order ID and ensure FDH or FxE prefix is removed
      const numericOrderId = orderId.toString().replace(/^(FDH|FxE)/i, '').replace(/\D/g, '');
      
      console.log("Passing order:", orderId, profitAmount, notes); // Log original ID for debugging
      console.log("Cleaned order ID:", numericOrderId); // Log cleaned ID for verification
      
      const formData = new FormData();
      formData.append('profit_amount', profitAmount.toString());
      formData.append('notes', notes);
      
      const response = await fetch('/api/proxy/ordors/pass_order/' + numericOrderId, {
        method: "POST",
        body: formData
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error response:", errorText);
        alert("Failed to pass order");
        return;
      }

      // Refresh the running orders list
      fetchRunningOrders();
      
      // Also refresh the passed orders list if it exists
      fetch('/api/proxy/ordors/passed_orders')
        .then((response) => response.json())
        .then((data) => {
          const formattedPassOrders = data.map((order: any) => ({
            id: order.pass_order_id,
            type: OrderType.STANDARD,
            order_id: order.order_id,
            user: {
              name: order.username,
              email: order.email,
            },
            amount: order.account_size,
            status: "Passed",
            createdAt: order.pass_date ? new Date(order.pass_date).toISOString().split("T")[0] : new Date().toISOString().split("T")[0],
            accountType: order.challenge_type,
            platformType: order.platform,
            profitTarget: order.profit_amount,
            notes: order.notes,
            txid: order.order_id
          }));
          setPassOrders([...formattedPassOrders].reverse());
        });

      alert("Order passed successfully!");
    } catch (error) {
      console.error("Error passing order:", error);
      alert("Failed to pass order");
    }
  };

  const handleFailRunningOrder = async (orderId: string) => {
    try {
      const response = await fetch('/api/proxy/ordors/fail_running_order/' + orderId, {
        method: "POST"
      })

      if (response.ok) {
        toast({
          title: "Success",
          description: "Order failed successfully",
        })
        refreshData()
      } else {
        throw new Error("Failed to fail order")
      }
    } catch (error) {
      console.error("Error failing order:", error)
      toast({
        title: "Error",
        description: "Failed to fail order. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleRejectOrder = async (order: OrderDetails, reason: RejectReasons) => {
    try {
      const formData = new FormData();
      formData.append('reason', reason);
      
      const response = await fetch('/api/proxy/ordors/reject_order/' + order.id, {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        alert(`Order ${order.id} rejected due to: ${reason}`);
      } else {
        console.error('Failed to reject order');
      }
    } catch (error) {
      console.error('Error rejecting order:', error);
    }
  }

  // Add these functions before handleFailOrder
  const fetchRunningOrders = async () => {
    try {
      const response = await fetch('/api/proxy/ordors/running_orders');
      const data = await response.json();
      setRunningOrders([...data].reverse());
    } catch (error) {
      console.error("Error fetching running orders:", error);
    }
  };

  const fetchFailedOrders = async () => {
    try {
      const response = await fetch('/api/proxy/ordors/failed_orders');
      const data = await response.json();
      const formattedFailedOrders = data.map((order: any) => ({
        id: order.fail_order_id,
        type: order.type as OrderType || OrderType.STANDARD,
        order_id: order.order_id,
        user: {
          name: order.username,
          email: order.email,
        },
        amount: order.account_size,
        status: OrderStatus.CANCELLED,
        createdAt: order.date || new Date().toISOString().split("T")[0],
        accountType: order.challenge_type,
        platformType: order.platform,
        paymentMethod: order.payment_method,
        txid: order.txid,
        reason: order.reason,
        server: order.server,
        platformLogin: order.platform_login,
        platformPassword: order.platform_password
      }));
      setFailedOrders([...formattedFailedOrders].reverse());
    } catch (error) {
      console.error("Error fetching failed orders:", error);
    }
  };

  const handleFailOrder = async (order: OrderDetails, reason: FailReasons) => {
    try {
      console.log("Failing order:", order.order_id, reason);
      
      // Clean the order ID by removing "FDH" or "FxE" prefix and extracting only numbers
      const cleanOrderId = order.order_id?.toString().replace(/^(FDH|FxE)/i, '').replace(/\D/g, '') || '';
      
      const formData = new FormData();
      formData.append('reason', reason);
      formData.append('date', new Date().toISOString());
      
      const response = await fetch('/api/proxy/ordors/fail_order/' + cleanOrderId, {
        method: "POST",
        body: formData
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error response:", errorText);
        throw new Error("Failed to fail order");
      }

      // Refresh the lists after successful failure
      await Promise.all([
        fetchRunningOrders(),
        fetchFailedOrders()
      ]);

      alert("Order failed successfully!");
    } catch (error) {
      console.error("Error failing order:", error);
      alert("Failed to fail order");
    }
  };

  // Update the filteredOrders to better handle status searches
  const filteredOrders = (
    selectedSection === "completedOrders" ? completedOrders : 
    selectedSection === "failedOrders" ? failedOrders :
    selectedSection === "passOrders" ? passOrders :
    selectedSection === "stageTwoOrders" ? stageTwoOrders :
    selectedSection === "liveOrders" ? liveOrders :
    selectedSection === "orders" ? orders.filter(order => order.status === OrderStatus.PENDING) :
    orders
  ).filter(
    (order) => {
      const searchLower = searchTerm.toLowerCase();
      
      // Handle different representations of the same status
      const statusMatches = (
        order.status.toLowerCase().includes(searchLower) ||
        (searchLower === "stage two" && (order.status === OrderStatus.STAGE_TWO || order.status === "Stage Two")) ||
        (searchLower === "live" && (order.status === OrderStatus.LIVE || order.status === "Live"))
      );
      
      return (
        order.user.name.toLowerCase().includes(searchLower) ||
        order.amount.toLowerCase().includes(searchLower) ||
        statusMatches ||
        (order.accountType || '').toLowerCase().includes(searchLower) ||
        (order.platformType || '').toLowerCase().includes(searchLower) ||
        (order.paymentMethod || '').toLowerCase().includes(searchLower) ||
        (order.txid || '').toLowerCase().includes(searchLower) ||
        (order.user.email || '').toLowerCase().includes(searchLower) ||
        (order.order_id || '').toLowerCase().includes(searchLower)
      );
    }
  )

  const filteredUsers = users.filter(
    (user) =>
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const filteredRunningOrders = runningOrders.filter(
    (order) => {
      // First check if the order matches the search term
      const matchesSearch = 
        order.order_id?.toString().toLowerCase().includes(searchTerm.toLowerCase()) ||
        (order.platform_login || '').toString().toLowerCase().includes(searchTerm.toLowerCase()) ||
        (order.platform_password || '').toString().toLowerCase().includes(searchTerm.toLowerCase()) ||
        (order.server || '').toString().toLowerCase().includes(searchTerm.toLowerCase()) ||
        (order.txid || '').toString().toLowerCase().includes(searchTerm.toLowerCase());

      // Then check if the order is not in live orders
      const isNotLive = !liveOrders.some(liveOrder => liveOrder.order_id === order.order_id);

      return matchesSearch && isNotLive;
    }
  )

  // Update getPaginatedData to handle different status representations
  const getPaginatedData = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    
    switch (selectedSection) {
      case "users":
        return filteredUsers.slice(startIndex, endIndex);
      case "runningOrders":
        return filteredRunningOrders.slice(startIndex, endIndex);
      case "orders":
        return filteredOrders.filter(order => order.status === OrderStatus.PENDING).slice(startIndex, endIndex);
      case "completedOrders":
        return filteredOrders.filter(order => 
          order.status === OrderStatus.COMPLETED || 
          order.status === "COMPLETED"
        ).slice(startIndex, endIndex);
      case "failedOrders":
        return filteredOrders.filter(order => 
          order.status === OrderStatus.CANCELLED || 
          order.status === "CANCELLED"
        ).slice(startIndex, endIndex);
      case "passOrders":
        return filteredOrders.filter(order => 
          order.status === "Passed"
        ).slice(startIndex, endIndex);
      case "stageTwoOrders":
        return filteredOrders.filter(order => 
          order.status === "Stage Two" || 
          order.status === OrderStatus.STAGE_TWO
        ).slice(startIndex, endIndex);
      case "liveOrders":
        return filteredOrders.filter(order => 
          order.status === "Live" ||
          order.status === OrderStatus.LIVE
        ).slice(startIndex, endIndex);
      default:
        return filteredOrders.slice(startIndex, endIndex);
    }
  }

  const paginatedData = getPaginatedData();

  // Add this helper function
  const getFilteredLength = () => {
    switch (selectedSection) {
      case "users":
        return filteredUsers.length;
      case "runningOrders":
        return filteredRunningOrders.length;
      case "passOrders":
      case "stageTwoOrders":
      case "liveOrders":
      case "completedOrders":
      case "failedOrders":
      default:
        return filteredOrders.length;
    }
  };

  const renderPaginationControls = () => (
    <div className="flex items-center justify-between mt-4">
      <div className="flex items-center gap-2">
        <select 
          value={itemsPerPage} 
          onChange={(e) => setItemsPerPage(Number(e.target.value))}
          className="bg-[#1E3A5F]/20 border border-[#1E3A5F] text-white rounded-lg text-xs px-2 py-1"
        >
          <option value={10}>10 per page</option>
          <option value={25}>25 per page</option>
          <option value={50}>50 per page</option>
          <option value={100}>100 per page</option>
        </select>
        <span className="text-gray-400 text-xs">
          Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, getFilteredLength())} of {getFilteredLength()} entries
        </span>
      </div>
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className="bg-[#1E3A5F]/20 border-[#1E3A5F] text-white hover:bg-[#1E3A5F]/40 rounded-lg text-xs px-3"
        >
          Previous
        </Button>
        <span className="text-white text-xs font-medium">
          Page {currentPage} of {totalPages}
        </span>
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className="bg-[#1E3A5F]/20 border-[#1E3A5F] text-white hover:bg-[#1E3A5F]/40 rounded-lg text-xs px-3"
        >
          Next
        </Button>
      </div>
    </div>
  )

  const renderRunningOrdersTable = () => (
    <div className="overflow-x-auto">
      <div className="mb-4">
        <Input
          placeholder="Search by Order ID, Platform Login, Password, Server, or Transaction ID..."
          value={searchTerm}
          onChange={handleSearch}
          className="max-w-sm bg-[#1E3A5F]/20 border-[#1E3A5F] text-white rounded-lg shadow-md text-sm"
        />
      </div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="text-xs">Order ID</TableHead>
            <TableHead className="text-xs">Platform Login</TableHead>
            <TableHead className="text-xs">Platform Password</TableHead>
            <TableHead className="text-xs">Server</TableHead>
            <TableHead className="text-xs">Session ID</TableHead>
            <TableHead className="text-xs">Terminal ID</TableHead>
            <TableHead className="text-xs">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {selectedSection === "runningOrders" && paginatedData.map((order) => (
            <TableRow key={(order as RunningOrder).order_id}>
              <TableCell className="text-xs">{(order as RunningOrder).order_id}</TableCell>
              <TableCell className="text-xs">{(order as RunningOrder).platform_login}</TableCell>
              <TableCell className="text-xs">{(order as RunningOrder).platform_password}</TableCell>
              <TableCell className="text-xs">{(order as RunningOrder).server}</TableCell>
              <TableCell className="text-xs">{(order as RunningOrder).session_id}</TableCell>
              <TableCell className="text-xs">{(order as RunningOrder).terminal_id}</TableCell>
              <TableCell>
                <div className="flex gap-2">
                  <PassOrderModal 
                    order={{
                      order_id: (order as RunningOrder).order_id,
                      ...order
                    }}
                    onPass={handlePassOrder}
                  />
                  <FailOrderModal
                    orderId={(order as RunningOrder).order_id}
                    onFail={handleFailOrder}
                  />
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      {renderPaginationControls()}
    </div>
  );

  const renderPaymentProofModal = () => (
    <PaymentProofModal
      order={showPaymentProof}
      onClose={() => setShowPaymentProof(null)}
    />
  )

  const renderTable = () => {
    console.log('renderTable called with selectedSection:', selectedSection);
    console.log('Current state:', {
      users: users.length,
      orders: orders.length,
      completedOrders: completedOrders.length,
      failedOrders: failedOrders.length,
      passOrders: passOrders.length,
      stageTwoOrders: stageTwoOrders.length,
      liveOrders: liveOrders.length,
      runningOrders: runningOrders.length
    });
    
    if (!isClient) {
      console.log('Not client, returning null');
      return null
    }

    switch (selectedSection) {
      case "users":
        console.log('Rendering users table with', users.length, 'users');
        return (
          <UsersTable
            users={users}
            searchTerm={searchTerm}
            currentPage={currentPage}
            itemsPerPage={itemsPerPage}
            onPageChange={handlePageChange}
            onSearch={handleSearch}
            onRefresh={refreshData}
            isRefreshing={isRefreshing}
            onEditUser={handleEditUser}
          />
        )
      case "runningOrders":
        console.log('Rendering running orders table with', runningOrders.length, 'orders');
        return renderRunningOrdersTable();
      case "orders":
      case "completedOrders":
      case "failedOrders":
      case "passOrders":
      case "stageTwoOrders":
      case "liveOrders":
        console.log('Rendering orders table for section:', selectedSection);
        console.log('paginatedData length:', paginatedData.length);
        console.log('paginatedData:', paginatedData);
        return (
          <div className="overflow-x-auto">
            <div className="mb-4">
              <Input
                placeholder="Search by name, email, status, account type, platform, payment method, or order ID..."
                value={searchTerm}
                onChange={handleSearch}
                className="max-w-sm bg-[#1E3A5F]/20 border-[#1E3A5F] text-white text-xs"
              />
            </div>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-xs">Order ID</TableHead>
                  <TableHead className="text-xs">Name</TableHead>
                  <TableHead className="text-xs">Account Type</TableHead>
                  <TableHead className="text-xs">Amount</TableHead>
                  <TableHead className="text-xs">Platform</TableHead>
                  <TableHead className="text-xs">Transaction ID</TableHead>
                  <TableHead className="text-xs">Status</TableHead>
                  {!isMobile && <TableHead className="text-xs">Created At</TableHead>}
                  <TableHead className="text-xs">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {["orders", "completedOrders", "failedOrders", "passOrders", "stageTwoOrders", "liveOrders"].includes(selectedSection || "") && paginatedData.map((order) => {
                  const orderWithChanges = localOrderChanges[order.id] || order;
                  const hasChanges = !!localOrderChanges[order.id];

                  return (
                    <TableRow key={order.id} className={hasChanges ? "bg-purple-900/10" : ""}>
                      <TableCell className="text-xs font-medium">{orderWithChanges.id}</TableCell>
                      <TableCell className="text-xs">{orderWithChanges.user.name}</TableCell>
                      <TableCell className="text-xs">{orderWithChanges.accountType}</TableCell>
                      <TableCell className="text-xs">${orderWithChanges.amount}</TableCell>
                      <TableCell className="text-xs">{orderWithChanges.platformType}</TableCell>
                      <TableCell className="text-xs">
                        {renderTxid(orderWithChanges.txid)}
                      </TableCell>
                      <TableCell className="text-xs">{getDisplayStatus(orderWithChanges.status || "")}</TableCell>
                      {!isMobile && <TableCell className="text-xs">{orderWithChanges.createdAt}</TableCell>}
                      <TableCell>
                        <div className={`flex ${isMobile ? 'flex-col' : 'flex-wrap'} gap-2`}>
                          <EditOrderModal
                            order={orderWithChanges}
                            onSave={handleSaveChanges}
                            onReject={handleRejectOrder}
                            onFail={handleFailOrder}
                          />
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleConfirmOrder(orderWithChanges)}
                            className={`w-full md:w-auto text-xs ${hasChanges ? 'bg-purple-500 text-white hover:bg-purple-600' : ''}`}
                          >
                            {hasChanges ? 'Confirm Changes' : 'Confirm'}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setShowPaymentProof(orderWithChanges)}
                            className="w-full md:w-auto bg-purple-500 text-white hover:bg-purple-600 text-xs"
                          >
                            Payment Proof
                          </Button>
                          <ViewOrderModal
                            order={orderWithChanges}
                            onView={handleViewOrder}
                            status={
                              selectedSection === "completedOrders" 
                                ? OrderStatus.COMPLETED 
                                : selectedSection === "failedOrders" 
                                  ? OrderStatus.CANCELLED 
                                  : selectedSection === "passOrders"
                                    ? "Passed"
                                    : selectedSection === "stageTwoOrders"
                                      ? "Stage Two"
                                      : selectedSection === "liveOrders"
                                        ? "Live"
                                    : orderWithChanges.status
                            }
                          />
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
            {renderPaginationControls()}
            {renderPaymentProofModal()}
          </div>
        )
      case "certificates":
        return <CertificateTable />
      default:
        console.log('No matching section, returning null');
        return null
    }
  }

  const getTotalPages = () => {
    let totalItems = 0
    if (selectedSection === "users") {
      totalItems = users.length
    } else if (selectedSection === "runningOrders") {
      totalItems = runningOrders.length
    }
    return Math.ceil(totalItems / itemsPerPage)
  }

  const handleEditUser = async (user: User, field: string) => {
    try {
      const endpoint = field === "hashed_password" 
        ? '/api/proxy/auth/user/' + user.id + '/update-password'
        : '/api/proxy/auth/update-user/' + user.id
      
      const response = await fetch(endpoint, {
          method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ [field]: user[field as keyof User] })
      })
      
      if (response.ok) {
        toast({ title: "Success", description: "User updated successfully" })
        refreshData()
      } else {
        throw new Error("Failed to update user")
      }
    } catch (error) {
      console.error("Error updating user:", error)
    toast({
        title: "Error",
        description: "Failed to update user. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Add missing utility functions
  const renderTxid = (txid: string | undefined) => {
    if (!txid) return "-"
    return (
      <div
        className="cursor-pointer"
        onClick={() => {
          navigator.clipboard.writeText(txid)
          toast.success("Transaction ID copied to clipboard")
        }}
      >
        {txid}
      </div>
    )
  }

  const getDisplayStatus = (status: OrderStatus) => {
    switch (status) {
      case "PENDING":
        return <Badge variant="default">{status}</Badge>
      case "ACTIVE":
        return <Badge variant="success">{status}</Badge>
      case "COMPLETED":
        return <Badge variant="success">{status}</Badge>
      case "FAILED":
        return <Badge variant="destructive">{status}</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  return (
    <motion.div
      className="space-y-8"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            {selectedSection === "users" && "Users"}
            {selectedSection === "orders" && "Orders"}
            {selectedSection === "completedOrders" && "Completed Orders"}
            {selectedSection === "failedOrders" && "Failed Orders"}
            {selectedSection === "passOrders" && "Passed Orders"}
            {selectedSection === "stageTwoOrders" && "Stage Two Orders"}
            {selectedSection === "liveOrders" && "Live Orders"}
            {selectedSection === "runningOrders" && "Running Orders"}
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Button
              onClick={refreshData}
              disabled={isRefreshing}
              className="h-8 w-8 p-0"
              variant="outline"
            >
              <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            </Button>
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search..."
                value={searchTerm}
                onChange={handleSearch}
                className="pl-8"
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {renderTable()}
        </CardContent>
      </Card>

      {selectedSection && (
        <Pagination
          currentPage={currentPage}
          totalPages={getTotalPages()}
          onPageChange={handlePageChange}
        />
      )}
    </motion.div>
  )
}

