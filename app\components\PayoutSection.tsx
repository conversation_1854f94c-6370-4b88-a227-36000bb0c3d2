'use client'

import { DollarSign, TrendingUp, Clock, Award, Briefcase } from 'lucide-react'
import Image from 'next/image'

export function PayoutSection() {
  return (
    <section className="py-20 relative overflow-hidden bg-gradient-to-b from-[#0A0F1C] to-black">
      <div className="container mx-auto px-4 relative z-10">
        {/* Simple Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-orange-500 via-blue-500 to-blue-600 bg-clip-text text-transparent mb-6">
            Horizon Trader Performance
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Witness real-time success stories from our top-performing traders
          </p>
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-orange-500/10 to-blue-500/10 rounded-xl p-8 border border-orange-500/20">
            <h3 className="text-2xl font-bold text-white mb-4">
              Join Our Success Stories
            </h3>
            <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
              Start your journey to becoming a funded trader. Join thousands of successful traders who have already achieved their goals.
            </p>
            <button className="bg-gradient-to-r from-orange-500 to-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:brightness-110 transition-all duration-300">
              Start Trading Today
            </button>
          </div>
        </div>

        {/* Giveaway & Referral Program Cards */}
        <div className="mt-20 grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {/* Giveaway Card */}
          <div className="bg-white/5 backdrop-blur-xl rounded-2xl p-8 border border-orange-500/20 shadow-lg flex flex-col items-center text-center">
            <h4 className="text-2xl font-bold bg-gradient-to-r from-orange-500 to-blue-600 bg-clip-text text-transparent mb-3">Giveaway Program</h4>
            <p className="text-gray-300 mb-6">Participate in our exclusive trading giveaways for a chance to win exciting rewards and boost your trading journey. Open to all active traders on our platform.</p>
            <a href="/dashboard/giveaway" className="mt-auto">
              <button className="bg-gradient-to-r from-orange-500 to-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:brightness-110 transition-all duration-300">
                Explore Giveaways
              </button>
            </a>
          </div>
          {/* Referral Card */}
          <div className="bg-white/5 backdrop-blur-xl rounded-2xl p-8 border border-blue-500/20 shadow-lg flex flex-col items-center text-center">
            <h4 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-orange-500 bg-clip-text text-transparent mb-3">Referral Program</h4>
            <p className="text-gray-300 mb-6">Invite friends and colleagues to join our platform and earn attractive rewards for every successful referral. Grow your network and your earnings with ease.</p>
            <a href="/dashboard/referal" className="mt-auto">
              <button className="bg-gradient-to-r from-blue-600 to-orange-500 text-white px-6 py-3 rounded-lg font-semibold hover:brightness-110 transition-all duration-300">
                Refer & Earn
              </button>
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}