"use client";
import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button"
import { Facebook, Instagram, Send, Twitter } from 'lucide-react'
import { DiscordLogoIcon } from "@radix-ui/react-icons"
import Link from 'next/link'
import { motion } from "framer-motion"

// Top-level server component
export function CommunityCallToAction() {
  return <CommunityCallToActionClient />;
}

function CommunityCallToActionClient() {
  // Throttle/Reduce any animation or array mapping here
  // Return the simplified JSX
  return (
    <section className="relative min-h-[400px] w-full bg-gradient-to-b from-[#0A0F1C] via-[#0F1A2E] to-[#121E36] py-16 sm:py-24 overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 bg-[url('/grid.svg')] opacity-10" />
      <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-transparent to-orange-900/10" />
      <div className="absolute top-0 right-0 w-[400px] h-[400px] bg-orange-500/10 rounded-full blur-[100px]" />
      <div className="absolute bottom-0 left-0 w-[400px] h-[400px] bg-blue-500/10 rounded-full blur-[100px]" />

      <div className="relative container mx-auto px-4 flex justify-center items-center min-h-[300px]">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.7 }}
          className="w-full max-w-3xl mx-auto bg-white/5 backdrop-blur-xl rounded-2xl p-10 border border-white/10 shadow-lg flex flex-col items-center text-center"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-orange-500 via-orange-400 to-blue-600 bg-clip-text text-transparent">
            Connect with Our Community
          </h2>
          <p className="text-lg text-gray-300 mb-8 max-w-xl mx-auto">
            Join our professional trading community for exclusive insights, support, and networking with top traders worldwide.
          </p>
          <Link href="https://discord.gg/ZzG8demuuz" target="_blank" rel="noopener noreferrer">
            <Button className="bg-gradient-to-r from-orange-500 to-blue-600 hover:from-orange-600 hover:to-blue-700 text-white px-8 py-5 text-lg font-bold rounded-xl shadow-lg transition-all duration-300 flex items-center gap-2 mb-8">
              <DiscordLogoIcon className="w-6 h-6" />
              Join Our Discord
            </Button>
          </Link>
          <div className="flex flex-wrap justify-center items-center gap-6 mt-2">
            {[
              {
                Icon: Send,
                link: "https://t.me/fundedhorizon",
                label: "Telegram",
                className: "hover:text-blue-400"
              },
              {
                Icon: Twitter,
                link: "https://twitter.com/fundedhorizon",
                label: "Twitter",
                className: "hover:text-blue-400"
              },
              {
                Icon: Instagram,
                link: "https://www.instagram.com/fundedhorizon",
                label: "Instagram",
                className: "hover:text-pink-400"
              },
              {
                Icon: Facebook,
                link: "https://www.facebook.com/fundedhorizon1",
                label: "Facebook",
                className: "hover:text-blue-500"
              },
              {
                Icon: DiscordLogoIcon,
                link: "https://discord.gg/ZzG8demuuz",
                label: "Discord",
                className: "hover:text-indigo-400"
              }
            ].map(({ Icon, link, label, className }) => (
              <a
                key={label}
                href={link}
                target="_blank"
                rel="noopener noreferrer"
                className={`rounded-full bg-white/10 p-3 transition-all duration-300 hover:bg-white/20 ${className}`}
                aria-label={label}
              >
                <Icon className="w-7 h-7 text-white" />
              </a>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
} 