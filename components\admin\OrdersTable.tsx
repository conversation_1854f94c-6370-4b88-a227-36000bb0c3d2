import { useState } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { OrderDetails, BaseOrderTableProps } from "./types"
import { BaseTable } from "./BaseTable"
import { Button } from "@/components/ui/button"
import { Eye, Edit } from "lucide-react"
import { Badge } from "@/components/ui/badge"

interface OrdersTableProps extends BaseOrderTableProps {
  isMobile: boolean
}

export function OrdersTable({
  orders,
  searchTerm,
  currentPage,
  itemsPerPage,
  onPageChange,
  onSearch,
  onRefresh,
  isRefreshing,
  onViewOrder,
  onEditOrder,
  onConfirmOrder,
  isMobile
}: OrdersTableProps) {
  const filteredOrders = orders.filter(order =>
    Object.values(order).some(value =>
      value?.toString().toLowerCase().includes(searchTerm.toLowerCase())
    )
  )

  const paginatedOrders = filteredOrders.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  )

  const getStatusColor = (status: string) => {
    switch (status) {
      case "PENDING":
        return "bg-yellow-500"
      case "COMPLETED":
        return "bg-green-500"
      case "CANCELLED":
        return "bg-red-500"
      default:
        return "bg-gray-500"
    }
  }

  return (
    <BaseTable
      data={orders}
      searchTerm={searchTerm}
      currentPage={currentPage}
      itemsPerPage={itemsPerPage}
      onPageChange={onPageChange}
      onSearch={onSearch}
      onRefresh={onRefresh}
      isRefreshing={isRefreshing}
      title="Orders"
    >
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Order ID</TableHead>
              <TableHead>User</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Type</TableHead>
              {!isMobile && <TableHead>Created At</TableHead>}
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedOrders.map((order) => (
              <TableRow key={order.id}>
                <TableCell>{order.id}</TableCell>
                <TableCell>{order.user.name}</TableCell>
                <TableCell>{order.amount}</TableCell>
                <TableCell>
                  <Badge className={getStatusColor(order.status)}>
                    {order.status}
                  </Badge>
                </TableCell>
                <TableCell>{order.type}</TableCell>
                {!isMobile && <TableCell>{order.createdAt}</TableCell>}
                <TableCell>
                  <div className={`flex ${isMobile ? 'flex-col' : ''} gap-2`}>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onViewOrder?.(order)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEditOrder?.(order)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    {order.status === "PENDING" && (
                      <Button
                        variant="default"
                        size="sm"
                        onClick={() => onConfirmOrder?.(order)}
                      >
                        Confirm
                      </Button>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </BaseTable>
  )
} 