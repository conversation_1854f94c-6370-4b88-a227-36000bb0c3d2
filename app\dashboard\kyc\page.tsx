'use client'

import { Head<PERSON> } from "@/components/header";
import { motion } from "framer-motion";
import { AlertCircle } from 'lucide-react';
import { useState } from 'react';

export default function KYCPage() {
  const [showMessage, setShowMessage] = useState(false);

  const handleApplyClick = () => {
    setShowMessage(true);
    setTimeout(() => setShowMessage(false), 4000);
  };

  return (
    <div className="min-h-screen relative bg-gradient-to-br from-blue-950 via-slate-900 to-orange-950 text-white overflow-x-hidden flex items-center justify-center p-4">
      {/* Animated background shapes */}
      <div className="pointer-events-none select-none absolute inset-0 z-0">
        <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-500/20 to-orange-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-tr from-orange-500/20 to-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute top-1/2 left-1/2 w-[600px] h-[600px] bg-gradient-to-r from-blue-500/10 to-orange-500/10 rounded-full blur-3xl animate-pulse delay-500 -translate-x-1/2 -translate-y-1/2" />
      </div>
      <div className="w-full max-w-2xl mx-auto z-10 relative">
        <div className="rounded-3xl shadow-2xl bg-gradient-to-br from-blue-900/80 via-slate-900/80 to-orange-900/80 border-2 border-blue-500/20 backdrop-blur-xl p-8 text-center">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-orange-400 via-orange-500 to-orange-600 bg-clip-text text-transparent mb-4">
            Complete Challenges to Get Your KYC
          </h1>
          <p className="text-lg text-blue-200/80 mb-8">
            Complete the required trading challenges to unlock KYC verification. Once eligible, click below to apply for KYC. Verification is fast and secure!
          </p>
          <button
            className="px-8 py-3 rounded-xl bg-gradient-to-r from-blue-500 to-orange-500 hover:from-blue-600 hover:to-orange-600 text-white font-semibold text-lg shadow-lg transition-all duration-300"
            onClick={handleApplyClick}
          >
            Apply for KYC
          </button>
          {showMessage && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              className="mt-6 flex items-center justify-center gap-3 bg-orange-500/10 border border-orange-400/30 rounded-xl px-6 py-4 text-orange-200 text-base font-medium shadow-lg"
            >
              <AlertCircle className="w-5 h-5 text-orange-400" />
              You are not able to apply until you complete the required trading challenges.
            </motion.div>
          )}
          <div className="mt-12 flex items-center justify-center gap-4">
            <AlertCircle className="w-5 h-5 text-orange-400" />
            <p className="text-sm text-blue-200/80">
              Complete verification within 5-10 minutes
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
