"use client";

import { <PERSON><PERSON> } from "@/components/ui/button"
import { motion } from "framer-motion"
import { TrendingUp, ArrowRight, Shield, Award, Globe, DollarSign, BarChart3, Target } from 'lucide-react'
import { useState, useEffect } from 'react'
import Image from 'next/image'

function useIsIPhone() {
  const [isIPhone, setIsIPhone] = useState(false);
  useEffect(() => {
    setIsIPhone(/iPhone|iPad|iPod/.test(navigator.userAgent));
  }, []);
  return isIPhone;
}

// 3D Animated Trading Sphere Component - Optimized for iPhone
const TradingSphere3D = ({ isBackground = false, isMobile = false, isIPhone = false }) => {
  // Ultra-simplified version for iPhone to prevent overheating
  if (isIPhone) {
    return (
      <div className={`relative w-full h-full flex items-center justify-center ${isBackground ? 'absolute inset-0 opacity-15' : ''}`}>
        <div className={`relative ${isBackground ? 'w-20 h-20' : 'w-28 h-28'}`}>
          {/* Static circles for iPhone - no animations to prevent heating */}
          <div className="absolute inset-0 rounded-full border-2 border-orange-500/20" />
          <div className="absolute inset-2 rounded-full border border-blue-500/20" />
          <div className="absolute inset-4 rounded-full border border-green-500/20" />
          {/* Single subtle pulse animation only */}
          <motion.div
            animate={{
              scale: [1, 1.05, 1],
              opacity: [0.3, 0.5, 0.3]
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute inset-6 rounded-full border border-orange-400/30"
          />
        </div>
      </div>
    )
  }

  // Simplified version for other mobile devices
  if (isMobile) {
    return (
      <div className={`relative w-full h-full flex items-center justify-center ${isBackground ? 'absolute inset-0 opacity-20' : ''}`}>
        <motion.div
          animate={{
            rotate: 360,
          }}
          transition={{
            duration: 40, // Slower animation for better performance
            repeat: Infinity,
            ease: "linear"
          }}
          className={`relative ${isBackground ? 'w-24 h-24' : 'w-32 h-32'}`}
        >
          <div className="absolute inset-0 rounded-full border-2 border-orange-500/30" />
          <div className="absolute inset-4 rounded-full border-2 border-blue-500/30" />
          <div className="absolute inset-8 rounded-full border-2 border-green-500/30" />
        </motion.div>
      </div>
    )
  }

  return (
    <div className={`relative w-full h-full flex items-center justify-center ${isBackground ? 'absolute inset-0 opacity-30' : ''}`}>
      {/* Main 3D Sphere */}
      <motion.div
        animate={{ 
          rotateY: 360,
          rotateX: 180,
        }}
        transition={{ 
          duration: 30, // Slower animation for better performance
          repeat: Infinity, 
          ease: "linear" 
        }}
        className={`relative ${isBackground ? 'w-48 h-48 sm:w-64 sm:h-64 md:w-80 md:h-80' : 'w-[16rem] h-[16rem] sm:w-[20rem] sm:h-[20rem] md:w-[28rem] md:h-[28rem] lg:w-96 lg:h-96'}`}
        style={{
          transformStyle: 'preserve-3d',
        }}
      >
        {/* Enhanced Sphere layers with more complexity */}
        {[...Array(isBackground ? 4 : 6)].map((_, i) => (
          <motion.div
            key={i}
            className={`absolute inset-0 rounded-full border-2 ${i % 3 === 0 ? 'border-orange-500/40' : i % 3 === 1 ? 'border-blue-500/40' : 'border-green-500/40'}`}
            style={{
              transform: `rotateY(${i * 30}deg) rotateX(${i * 25}deg) rotateZ(${i * 15}deg)`,
              transformStyle: 'preserve-3d',
            }}
            animate={{
              rotateZ: [0, 360],
              scale: [1, 1.1, 1],
            }}
            transition={{
              duration: 20 + i * 1.5, // Slower animations
              repeat: Infinity,
              ease: "linear",
              delay: i * 0.3,
            }}
          />
        ))}
        
        {/* Inner rotating elements with enhanced effects */}
        <motion.div
          animate={{ 
            rotate: 360,
            scale: [1, 1.05, 1],
          }}
          transition={{ 
            duration: 18, // Slower animation
            repeat: Infinity, 
            ease: "linear" 
          }}
          className="absolute inset-8 rounded-full border-2 border-blue-500/50 shadow-lg shadow-blue-500/20"
        />
        
        <motion.div
          animate={{ 
            rotate: -360,
            scale: [1, 0.95, 1],
          }}
          transition={{ 
            duration: 25, // Slower animation
            repeat: Infinity, 
            ease: "linear" 
          }}
          className="absolute inset-16 rounded-full border-2 border-green-500/40 shadow-lg shadow-green-500/20"
        />
        
        {!isBackground && (
          <motion.div
            animate={{ 
              rotate: 360,
              scale: [1, 1.1, 1],
            }}
            transition={{ 
              duration: 35, // Slower animation
              repeat: Infinity, 
              ease: "linear" 
            }}
            className="absolute inset-24 rounded-full border border-purple-500/30 shadow-lg shadow-purple-500/20"
          />
        )}
        
        {/* Enhanced floating trading icons with glow effects */}
        <motion.div
          animate={{ 
            y: [0, -25, 0],
            rotate: [0, 360],
            scale: [1, 1.2, 1],
          }}
          transition={{ 
            duration: 8, // Slower animation
            repeat: Infinity, 
            ease: "easeInOut" 
          }}
          className="absolute top-4 left-1/2 -translate-x-1/2"
        >
          <div className="relative">
            <DollarSign className={`${isBackground ? 'w-5 h-5 sm:w-6 sm:h-6 md:w-8 md:h-8' : 'w-6 h-6 sm:w-8 sm:h-8'} text-orange-400 drop-shadow-lg`} />
            <div className="absolute inset-0 w-full h-full bg-orange-400/20 rounded-full blur-md animate-pulse"></div>
          </div>
        </motion.div>
        
        <motion.div
          animate={{ 
            y: [0, 25, 0],
            rotate: [0, -360],
            scale: [1, 1.2, 1],
          }}
          transition={{ 
            duration: 10, // Slower animation
            repeat: Infinity, 
            ease: "easeInOut",
            delay: 1
          }}
          className="absolute bottom-4 left-1/2 -translate-x-1/2"
        >
          <div className="relative">
            <TrendingUp className={`${isBackground ? 'w-5 h-5 sm:w-6 sm:h-6 md:w-8 md:h-8' : 'w-6 h-6 sm:w-8 sm:h-8'} text-green-400 drop-shadow-lg`} />
            <div className="absolute inset-0 w-full h-full bg-green-400/20 rounded-full blur-md animate-pulse"></div>
          </div>
        </motion.div>
        
        {!isBackground && (
          <>
            <motion.div
              animate={{ 
                x: [0, 25, 0],
                rotate: [0, 360],
                scale: [1, 1.2, 1],
              }}
              transition={{ 
                duration: 9, // Slower animation
                repeat: Infinity, 
                ease: "easeInOut",
                delay: 2
              }}
              className="absolute top-1/2 right-4 -translate-y-1/2"
            >
              <div className="relative">
                <BarChart3 className="w-6 h-6 sm:w-8 sm:h-8 text-blue-400 drop-shadow-lg" />
                <div className="absolute inset-0 w-full h-full bg-blue-400/20 rounded-full blur-md animate-pulse"></div>
              </div>
            </motion.div>
            
            <motion.div
              animate={{ 
                x: [0, -25, 0],
                rotate: [0, -360],
                scale: [1, 1.2, 1],
              }}
              transition={{ 
                duration: 11, // Slower animation
                repeat: Infinity, 
                ease: "easeInOut",
                delay: 3
              }}
              className="absolute top-1/2 left-4 -translate-y-1/2"
            >
              <div className="relative">
                <Target className="w-6 h-6 sm:w-8 sm:h-8 text-purple-400 drop-shadow-lg" />
                <div className="absolute inset-0 w-full h-full bg-purple-400/20 rounded-full blur-md animate-pulse"></div>
              </div>
            </motion.div>
          </>
        )}
        
        {/* Enhanced central glow effect */}
        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-orange-500/10 via-blue-500/10 to-green-500/10 blur-xl animate-pulse"></div>
      </motion.div>
    </div>
  )
}

// Trading Animation Background Component
const TradingAnimation = () => {
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  return (
    <div className="absolute inset-0 overflow-hidden">
      {/* Animated background elements */}
      <motion.div
        animate={{ 
          x: [0, 100, 0],
          y: [0, -50, 0],
          rotate: [0, 360],
        }}
        transition={{ 
          duration: 20, // Slower animation
          repeat: Infinity, 
          ease: "linear" 
        }}
        className="absolute top-20 left-10 bg-gradient-to-r from-orange-500/20 to-blue-500/20 backdrop-blur-sm rounded-lg p-4 border border-white/20 hidden md:block"
      >
        <div className="flex items-center gap-3">
          <Shield className="w-6 h-6 text-orange-400" />
          <div>
            <div className="text-white font-bold text-lg">100%</div>
            <div className="text-gray-300 text-xs">Secure</div>
          </div>
        </div>
      </motion.div>
      
      <motion.div
        animate={{ 
          x: [0, -80, 0],
          y: [0, 60, 0],
          rotate: [0, -360],
        }}
        transition={{ 
          duration: 25, // Slower animation
          repeat: Infinity, 
          ease: "linear",
          delay: 2
        }}
        className="absolute top-40 right-20 bg-gradient-to-r from-blue-500/20 to-green-500/20 backdrop-blur-sm rounded-lg p-4 border border-white/20 hidden md:block"
      >
        <div className="flex items-center gap-3">
          <Award className="w-6 h-6 text-blue-400" />
          <div>
            <div className="text-white font-bold text-lg">24/7</div>
            <div className="text-gray-300 text-xs">Support</div>
          </div>
        </div>
      </motion.div>
      
      <motion.div
        animate={{ 
          x: [0, 60, 0],
          y: [0, -40, 0],
          rotate: [0, 360],
        }}
        transition={{ 
          duration: 18, // Slower animation
          repeat: Infinity, 
          ease: "linear",
          delay: 4
        }}
        className="absolute bottom-40 left-20 bg-gradient-to-r from-green-500/20 to-purple-500/20 backdrop-blur-sm rounded-lg p-4 border border-white/20 hidden md:block"
      >
        <div className="flex items-center gap-3">
          <Globe className="w-6 h-6 text-green-400" />
          <div>
            <div className="text-white font-bold text-lg">150+</div>
            <div className="text-gray-300 text-xs">Countries</div>
            </div>
        </div>
      </motion.div>
      
      <motion.div
        animate={{ rotate: [0, 5, -5, 0] }}
        transition={{ duration: 4, repeat: Infinity }}
        className="absolute top-1/2 right-10 bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-sm rounded-lg p-4 border border-white/20 hidden md:block"
      >
        <div className="flex items-center gap-3">
          <TrendingUp className="w-6 h-6 text-purple-400" />
          <div>
            <div className="text-white font-bold text-lg">90%</div>
            <div className="text-gray-300 text-xs">Profit Split</div>
          </div>
        </div>
      </motion.div>

      {/* Grid pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }} />
      </div>
    </div>
  )
}

export default function Hero() {
  const [isMobile, setIsMobile] = useState(false)
  const isIPhone = useIsIPhone();

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // iPhone-specific performance optimization
  useEffect(() => {
    if (isIPhone) {
      // Disable complex animations for iPhone
      document.documentElement.style.setProperty('--animation-duration', '0.5s');
      document.documentElement.style.setProperty('--reduce-motion', '1');
    }
  }, [isIPhone])

  return (
    <section className={`relative ${isIPhone ? 'min-h-screen-ios' : 'min-h-screen'} pt-16 sm:pt-20 overflow-hidden bg-gradient-to-b from-[#0A0F1C] via-[#0F1A2E] to-[#121E36]`}>
      {/* Trading Animation Background */}
      <TradingAnimation />

      {/* Main Content */}
      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-7xl mx-auto">
          {/* Single Responsive Layout */}
          <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center pt-12 lg:pt-20 pb-12 lg:pb-16">
            {/* Left Side - 3D Animated Component (Hidden on mobile, shown on desktop) */}
            <div className="hidden lg:flex relative h-[700px] items-center justify-center">
              <TradingSphere3D isIPhone={isIPhone} />
            </div>

            {/* Mobile 3D Background (Shown only on mobile) */}
            <div className="lg:hidden absolute inset-0 flex items-center justify-center opacity-30">
              <TradingSphere3D isBackground={true} isMobile={true} isIPhone={isIPhone} />
            </div>
            
            {/* Right Side - Content (Centered on mobile, left-aligned on desktop) */}
            <div className={`relative z-10 ${isMobile ? 'text-center' : 'text-left'}`}>
              {/* Single XXL Bold Heading */}
              <motion.h1
                initial={{ opacity: 0, y: isMobile ? 20 : 0, x: isMobile ? 0 : 50 }}
                animate={{ opacity: 1, y: 0, x: 0 }}
                transition={{ duration: 0.8 }}
                className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl 2xl:text-8xl font-black leading-tight tracking-tight mb-6 lg:mb-8"
              >
                <span className="text-white block mb-2 lg:mb-4">
                  TRADE
                </span>
                <span className="bg-gradient-to-r from-orange-500 via-orange-400 to-blue-600 bg-clip-text text-transparent block">
                  LIKE A PRO
                </span>
              </motion.h1>

              {/* Subtitle */}
              <motion.p
                initial={{ opacity: 0, y: isMobile ? 15 : 0, x: isMobile ? 0 : 30 }}
                animate={{ opacity: 1, y: 0, x: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="text-base sm:text-lg lg:text-xl xl:text-2xl text-gray-300 max-w-2xl mb-8 lg:mb-12 leading-relaxed"
              >
                Get funded up to <span className="text-orange-500 font-bold">$2.5M</span> with our professional trading platform. 
                Join thousands of successful traders worldwide.
              </motion.p>

              {/* CTA Buttons */}
              <motion.div
                initial={{ opacity: 0, y: isMobile ? 15 : 0, x: isMobile ? 0 : 30 }}
                animate={{ opacity: 1, y: 0, x: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className={`flex ${isMobile ? 'flex-col gap-4' : 'flex-col sm:flex-row gap-6'} mb-8 lg:mb-16`}
              >
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-orange-500 to-blue-600 hover:from-orange-600 hover:to-blue-700 text-white px-8 py-4 lg:py-6 text-lg font-bold rounded-xl shadow-2xl shadow-orange-500/25 transition-all duration-300 hover:scale-105"
                >
                  Start Trading Now
                  <ArrowRight className="ml-2 h-5 w-5 lg:h-6 lg:w-6" />
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  className="bg-white/5 text-white border-white/20 hover:bg-white/10 px-8 py-4 lg:py-6 text-lg font-bold rounded-xl transition-all duration-300 hover:scale-105"
                >
                  Learn More
                </Button>
              </motion.div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom wave effect */}
      <div className="absolute bottom-0 left-0 right-0">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" className="w-full h-20">
          <path
            d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z"
            opacity=".25"
            fill="url(#wave-gradient)"
          />
          <path
            d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z"
            opacity=".5"
            fill="url(#wave-gradient)"
          />
          <path
            d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z"
            fill="url(#wave-gradient)"
          />
          <defs>
            <linearGradient id="wave-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#f97316" />
              <stop offset="50%" stopColor="#3b82f6" />
              <stop offset="100%" stopColor="#f97316" />
            </linearGradient>
          </defs>
        </svg>
      </div>
    </section>
  )
}
