export enum OrderStatus {
  PENDING = "PENDING",
  COMPLETED = "COMPLETED",
  CANCELLED = "CANCELLED",
  STAGE_TWO = "STAGE_TWO",
  LIVE = "LIVE"
}

export enum OrderType {
  STANDARD = "STANDARD",
  CUSTOM = "CUSTOM"
}

export enum RejectReasons {
  INVALID_PAYMENT = "Invalid Payment",
  INSUFFICIENT_AMOUNT = "Insufficient Amount",
  WRONG_ACCOUNT_TYPE = "Wrong Account Type",
  OTHER = "Other"
}

export enum FailReasons {
  DAILY_DRAWDOWN_5 = "Policy Violation Detection - 5% Daily Drawdown Exceeded",
  OVERALL_DRAWDOWN = "Breach Alert - Overall Drawdown Limit Violated",
  PHASE1_TRADING_DAYS = "Trading Breach - Phase-1 Minimum Trading Days Not Met",
  DAILY_DRAWDOWN_4 = "Policy Violation Detection - 4% Daily Drawdown Exceeded",
  PHASE2_TRADING_DAYS = "Trading Breach - Phase-2 Minimum Trading Days Requirement Unfulfilled",
 
}

interface OrderImage {
  image_url: string;
  created_at: string;
}

export interface OrderDetails {
  id: number | string;
  user: {
    name: string;
    email: string;
    password?: string;
    hashed_password?: string;
  };
  amount: string;
  status: OrderStatus | string;
  type: OrderType;
  createdAt: string;
  accountType: string;
  platformType: string;
  platformLogin?: string;
  platformPassword?: string;
  server?: string;
  sessionId?: string;
  terminalId?: string;
  startingBalance?: number;
  currentBalance?: number;
  profitTarget?: number;
  paymentProof?: string;
  paymentMethod?: string;
  txid?: string;
  order_id?: string;
  reason?: string;
  notes?: string;
  image?: OrderImage;
}

export interface ExtendedOrderDetails extends Omit<OrderDetails, 'terminalId'> {
  terminalId?: string;
} 
