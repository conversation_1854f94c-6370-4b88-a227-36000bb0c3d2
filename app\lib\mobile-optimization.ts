// Mobile optimization utilities for better performance on mobile devices

export const isMobile = (): boolean => {
  if (typeof window === 'undefined') return false;
  return window.innerWidth < 768;
};

export const isIOS = (): boolean => {
  if (typeof window === 'undefined') return false;
  return /iPad|iPhone|iPod/.test(navigator.userAgent);
};

export const isSafari = (): boolean => {
  if (typeof window === 'undefined') return false;
  return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
};

export const isMobileSafari = (): boolean => {
  return isMobile() && isSafari();
};

export const isIPhone = (): boolean => {
  if (typeof window === 'undefined') return false;
  return /iPhone/.test(navigator.userAgent);
};

// Reduce animation complexity on mobile
export const getAnimationDuration = (baseDuration: number): number => {
  if (isMobile()) {
    return baseDuration * 1.5; // Slower animations on mobile
  }
  return baseDuration;
};

// Reduce the number of animated elements on mobile
export const getAnimationCount = (baseCount: number): number => {
  if (isMobile()) {
    return Math.max(1, Math.floor(baseCount / 2)); // Half the animations on mobile
  }
  return baseCount;
};

// Optimize for mobile performance
export const optimizeForMobile = () => {
  if (typeof window === 'undefined') return;

  // Reduce motion if user prefers it
  if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
    document.documentElement.style.setProperty('--animation-duration', '0.1s');
  }

  // Optimize for mobile Safari
  if (isMobileSafari()) {
    // Add specific optimizations for iOS Safari
    document.documentElement.style.setProperty('--webkit-transform', 'translateZ(0)');
    
    // Fix viewport height issues on iOS
    const setVH = () => {
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty('--vh', vh + 'px');
    };
    
    setVH();
    window.addEventListener('resize', setVH);
    window.addEventListener('orientationchange', setVH);
  }
  
  // iPhone-specific optimizations
  if (isIPhone()) {
    // Add hardware acceleration
    document.body.style.webkitTransform = 'translateZ(0)';
    document.body.style.transform = 'translateZ(0)';
    
    // Optimize touch scrolling
    (document.body.style as any).webkitOverflowScrolling = 'touch';
    
    // Fix for iPhone Safari flexbox issues
    const style = document.createElement('style');
    style.textContent = `
      @supports (-webkit-touch-callout: none) {
        .flex {
          display: -webkit-box;
          display: -webkit-flex;
          display: -ms-flexbox;
          display: flex;
        }
        
        .flex-col {
          -webkit-box-orient: vertical;
          -webkit-box-direction: normal;
          -webkit-flex-direction: column;
          -ms-flex-direction: column;
          flex-direction: column;
        }
        
        .flex-row {
          -webkit-box-orient: horizontal;
          -webkit-box-direction: normal;
          -webkit-flex-direction: row;
          -ms-flex-direction: row;
          flex-direction: row;
        }
        
        .justify-center {
          -webkit-box-pack: center;
          -webkit-justify-content: center;
          -ms-flex-pack: center;
          justify-content: center;
        }
        
        .items-center {
          -webkit-box-align: center;
          -webkit-align-items: center;
          -ms-flex-align: center;
          align-items: center;
        }
      }
    `;
    document.head.appendChild(style);
  }
};

// Debounce function for better performance
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

// Throttle function for better performance
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};

// Optimize scroll events for mobile
export const optimizeScroll = (callback: () => void) => {
  if (isMobile()) {
    return throttle(callback, 16); // ~60fps
  }
  return callback;
};

// Optimize resize events for mobile
export const optimizeResize = (callback: () => void) => {
  return debounce(callback, 250);
};

// Check if device supports touch
export const supportsTouch = (): boolean => {
  if (typeof window === 'undefined') return false;
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
};

// Get device pixel ratio for better image loading
export const getDevicePixelRatio = (): number => {
  if (typeof window === 'undefined') return 1;
  return window.devicePixelRatio || 1;
};

// Optimize image loading for mobile
export const getOptimizedImageSize = (baseSize: number): number => {
  const pixelRatio = getDevicePixelRatio();
  if (isMobile()) {
    return Math.min(baseSize, baseSize * pixelRatio);
  }
  return baseSize;
};

// iPhone-specific viewport height fix
export const fixIPhoneViewport = () => {
  if (typeof window === 'undefined') return;
  
  if (isIPhone()) {
    const setVH = () => {
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty('--vh', vh + 'px');
    };
    
    setVH();
    window.addEventListener('resize', setVH);
    window.addEventListener('orientationchange', setVH);
    
    return () => {
      window.removeEventListener('resize', setVH);
      window.removeEventListener('orientationchange', setVH);
    };
  }
};

// Optimize loading for iPhone
export const optimizeIPhoneLoading = () => {
  if (typeof window === 'undefined') return;

  if (isIPhone()) {
    // Add hardware acceleration
    document.body.style.webkitTransform = 'translateZ(0)';
    document.body.style.transform = 'translateZ(0)';

    // Drastically reduce animation complexity for iPhone
    document.documentElement.style.setProperty('--animation-duration', '3s');
    document.documentElement.style.setProperty('--animation-delay', '0.5s');

    // Disable complex animations on iPhone
    document.documentElement.style.setProperty('--disable-complex-animations', '1');

    // Reduce motion for better performance
    document.documentElement.style.setProperty('--reduce-motion', '1');

    // Fix viewport height
    fixIPhoneViewport();

    // Optimize touch scrolling
    (document.body.style as any).webkitOverflowScrolling = 'touch';

    // Disable WebGL/3D graphics on iPhone
    document.documentElement.style.setProperty('--disable-webgl', '1');

    // Reduce image quality for faster loading
    document.documentElement.style.setProperty('--image-quality', 'low');
  }
};

// Check if device needs performance optimization
export const needsPerformanceOptimization = () => {
  if (typeof window === 'undefined') return false;

  // iPhone specifically needs optimization due to heating issues
  return isIPhone();
};

// Disable heavy components on iPhone
export const shouldDisableHeavyComponents = () => {
  return needsPerformanceOptimization();
};