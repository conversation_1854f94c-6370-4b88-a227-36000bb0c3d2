import { OrderStatus, OrderType, FailReasons } from "@/types/order"

export interface User {
  id: number
  username: string
  email: string
  name: string
  country: string
  phone_no: string
  address: string
  hashed_password?: string
  createdAt: string
}

export interface OrderUser {
  id: number
  username: string
  email: string
}

export interface OrderDetails {
  id: string | number
  order_id: string
  user: OrderUser
  amount: number
  status: OrderStatus
  type: OrderType
  createdAt: string
  accountType: string
  platformType: string
  platformLogin: string
  platformPassword: string
  server: string
  sessionId: string
  terminalId: string
  startingBalance: number
  currentBalance: number
  profitTarget: number
  paymentMethod: string
  txid?: string
  reason?: FailReasons
  notes?: string
  image?: string
  paymentProof?: string
}

export interface RunningOrder {
  id: string
  order_id: string
  platform_login: string
  platform_password: string
  server: string
  session_id: string
  terminal_id: string
  txid?: string
}

export interface AdminTablesProps {
  selectedSection?: string
}

export interface TableProps {
  data: any[]
  searchTerm: string
  currentPage: number
  itemsPerPage: number
  onPageChange: (page: number) => void
  onSearch: (e: React.ChangeEvent<HTMLInputElement>) => void
  onRefresh: () => void
  isRefreshing: boolean
  title: string
  children: React.ReactNode
}

export interface BaseOrderTableProps {
  orders: OrderDetails[]
  searchTerm: string
  currentPage: number
  itemsPerPage: number
  onPageChange: (page: number) => void
  onSearch: (e: React.ChangeEvent<HTMLInputElement>) => void
  onRefresh: () => void
  isRefreshing: boolean
  onViewOrder?: (order: OrderDetails) => void
  onEditOrder?: (order: OrderDetails) => void
  onConfirmOrder?: (order: OrderDetails) => void
} 