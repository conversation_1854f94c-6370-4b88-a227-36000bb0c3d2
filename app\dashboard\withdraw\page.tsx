'use client'

import { useState } from "react"
import { motion } from "framer-motion"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Shield, AlertTriangle, CheckCircle, ArrowRight } from "lucide-react"

export default function WithdrawPage() {
  const router = useRouter()
  const [option, setOption] = useState("")

  const handleKYCRedirect = () => {
    router.push('/dashboard/kyc')
  }

  return (
    <div className="min-h-screen relative bg-gradient-to-br from-blue-950 via-slate-900 to-orange-950 text-white overflow-x-hidden flex items-center justify-center p-4">
      {/* Animated background shapes */}
      <div className="pointer-events-none select-none absolute inset-0 z-0">
        <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-500/20 to-orange-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-tr from-orange-500/20 to-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute top-1/2 left-1/2 w-[600px] h-[600px] bg-gradient-to-r from-blue-500/10 to-orange-500/10 rounded-full blur-3xl animate-pulse delay-500 -translate-x-1/2 -translate-y-1/2" />
      </div>
      <div className="w-full max-w-2xl mx-auto z-10 relative">
        <div className="rounded-3xl shadow-2xl bg-gradient-to-br from-blue-900/80 via-slate-900/80 to-orange-900/80 border-2 border-blue-500/20 backdrop-blur-xl p-6 md:p-8 text-center">
          <div className="flex items-center justify-center gap-3 mb-6">
            <div className="w-12 h-12 rounded-full bg-gradient-to-r from-orange-500 to-orange-600 flex items-center justify-center">
              <Shield className="h-6 w-6 text-white" />
            </div>
            <h1 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-orange-400 via-orange-500 to-orange-600 bg-clip-text text-transparent">
              Withdraw
            </h1>
          </div>

          {/* KYC Verification Required Message */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8 p-6 rounded-2xl bg-gradient-to-r from-yellow-500/10 to-orange-500/10 border-2 border-yellow-500/20 shadow-lg"
          >
            <div className="flex items-center justify-center gap-3 mb-4">
              <div className="w-10 h-10 rounded-full bg-gradient-to-r from-yellow-500 to-orange-500 flex items-center justify-center">
                <AlertTriangle className="h-5 w-5 text-white" />
              </div>
              <h2 className="text-xl md:text-2xl font-semibold text-yellow-400">
                KYC Verification Required
              </h2>
            </div>
            <p className="text-white text-base md:text-lg mb-6 leading-relaxed">
              To withdraw your rewards and profits, you must first complete your KYC (Know Your Customer) verification. 
              This is a mandatory security requirement to ensure safe and compliant transactions.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center items-center">
              <Button
                onClick={handleKYCRedirect}
                className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-orange-500/25 flex items-center gap-2"
              >
                <CheckCircle className="h-4 w-4" />
                Complete KYC Verification
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
          </motion.div>

          {/* Withdraw Options (Disabled until KYC is complete) */}
          <div className="space-y-4">
            <h3 className="text-lg md:text-xl font-semibold text-gray-300 mb-4">
              Withdrawal Options
            </h3>
            <select
              value={option}
              onChange={e => setOption(e.target.value)}
              disabled
              className="w-full max-w-xs mx-auto px-4 py-3 rounded-xl bg-slate-900/50 border-2 border-gray-600/30 text-gray-400 text-lg focus:outline-none transition-all cursor-not-allowed opacity-50"
            >
              <option value="">Select Withdraw Option</option>
              <option value="points">Withdraw Points</option>
              <option value="reward">Withdraw Challenge Reward</option>
            </select>
            
            {option && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mt-6 p-6 rounded-2xl bg-gradient-to-r from-gray-500/10 to-gray-600/10 border-2 border-gray-500/20 text-lg font-semibold text-gray-400 shadow-lg"
              >
                <div className="flex items-center justify-center gap-2 mb-2">
                  <AlertTriangle className="h-5 w-5 text-gray-400" />
                  <span>KYC Verification Required</span>
                </div>
                <p className="text-sm text-gray-500">
                  Please complete your KYC verification first to access withdrawal features.
                </p>
              </motion.div>
            )}
          </div>

          {/* Additional Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="mt-8 p-4 rounded-xl bg-gradient-to-r from-blue-500/5 to-orange-500/5 border border-blue-500/10"
          >
            <h4 className="text-sm font-semibold text-blue-400 mb-2">Why KYC is Required?</h4>
            <ul className="text-xs md:text-sm text-gray-300 space-y-1 text-left">
              <li>• Ensures account security and prevents fraud</li>
              <li>• Required by financial regulations and compliance</li>
              <li>• Enables secure withdrawal processing</li>
              <li>• Protects your funds and personal information</li>
            </ul>
          </motion.div>
        </div>
      </div>
    </div>
  )
}
