import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { motion } from "framer-motion"
import {
  Facebook,
  Instagram,
  Mail,
  ExternalLink,
  FileText,
  Shield,
  AlertCircle,
  ChevronRight,
  Send,
  BookOpen,
  GraduationCap,
  HelpCircle,
  Building,
  MessageSquare
} from 'lucide-react'
import { DiscordLogoIcon } from "@radix-ui/react-icons"
import Link from "next/link"

export function Footer() {
  return (
    <footer className="relative bg-[#0A0F1C] text-white border-t border-white/10">
      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-[url('/grid.svg')] opacity-10" />
        <div className="absolute w-[800px] h-[800px] -left-96 top-0 bg-blue-600/5 rounded-full blur-[120px]" />
        <div className="absolute w-[800px] h-[800px] -right-96 bottom-0 bg-blue-600/5 rounded-full blur-[120px]" />
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-blue-950/10 to-black/20" />
      </div>

      {/* Main footer content */}
      <div className="container mx-auto px-4 sm:px-6 relative z-10">
        {/* Top section with logo and main links */}
        <div className="pt-16 pb-12 border-b border-white/10">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10">
            {/* Brand section */}
            <div className="space-y-6">
              <h2 className="text-2xl font-bold bg-gradient-to-r from-orange-500 to-blue-600 bg-clip-text text-transparent">
                Funded Horizon
              </h2>
              <p className="text-gray-400 text-sm leading-relaxed">
                Professional trading challenges and funded accounts for serious traders. Access institutional-grade tools and support to elevate your trading career.
              </p>
              <div className="flex space-x-4">
                <a
                  href="https://www.facebook.com/fundedhorizon1/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-2 rounded-full bg-white/5 hover:bg-white/10 transition-colors duration-300"
                  aria-label="Facebook"
                >
                  <Facebook className="w-5 h-5 text-gray-300" />
                </a>
                <a
                  href="https://t.me/fundedhorizon"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-2 rounded-full bg-white/5 hover:bg-white/10 transition-colors duration-300"
                  aria-label="Telegram"
                >
                  <Send className="w-5 h-5 text-gray-300" />
                </a>
                <a
                  href="https://www.instagram.com/fundedhorizon"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-2 rounded-full bg-white/5 hover:bg-white/10 transition-colors duration-300"
                  aria-label="Instagram"
                >
                  <Instagram className="w-5 h-5 text-gray-300" />
                </a>
                <a
                  href="https://discord.gg/ZzG8demuuz"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-2 rounded-full bg-white/5 hover:bg-white/10 transition-colors duration-300"
                  aria-label="Discord"
                >
                  <DiscordLogoIcon className="w-5 h-5 text-gray-300" />
                </a>
              </div>
            </div>

            {/* Platform links */}
            <div className="space-y-6">
              <h3 className="text-sm font-semibold uppercase tracking-wider text-orange-500">Platform</h3>
              <ul className="space-y-3">
                <li>
                  <Link href="#" className="text-gray-300 hover:text-white text-sm flex items-center gap-2 group transition-colors duration-200">
                    <GraduationCap className="w-4 h-4 text-orange-500/70" />
                    <span className="group-hover:translate-x-1 transition-transform duration-200">Trading Challenges</span>
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-gray-300 hover:text-white text-sm flex items-center gap-2 group transition-colors duration-200">
                    <Shield className="w-4 h-4 text-orange-500/70" />
                    <span className="group-hover:translate-x-1 transition-transform duration-200">Risk Management</span>
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-gray-300 hover:text-white text-sm flex items-center gap-2 group transition-colors duration-200">
                    <BookOpen className="w-4 h-4 text-orange-500/70" />
                    <span className="group-hover:translate-x-1 transition-transform duration-200">Learning Resources</span>
                  </Link>
                </li>
              </ul>
            </div>

            {/* Company links */}
            <div className="space-y-6">
              <h3 className="text-sm font-semibold uppercase tracking-wider text-orange-500">Company</h3>
              <ul className="space-y-3">
                <li>
                  <Link href="#" className="text-gray-300 hover:text-white text-sm flex items-center gap-2 group transition-colors duration-200">
                    <Building className="w-4 h-4 text-orange-500/70" />
                    <span className="group-hover:translate-x-1 transition-transform duration-200">About Us</span>
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-gray-300 hover:text-white text-sm flex items-center gap-2 group transition-colors duration-200">
                    <MessageSquare className="w-4 h-4 text-orange-500/70" />
                    <span className="group-hover:translate-x-1 transition-transform duration-200">Contact</span>
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-gray-300 hover:text-white text-sm flex items-center gap-2 group transition-colors duration-200">
                    <HelpCircle className="w-4 h-4 text-orange-500/70" />
                    <span className="group-hover:translate-x-1 transition-transform duration-200">FAQ</span>
                  </Link>
                </li>
              </ul>
            </div>

            {/* Support section */}
            <div className="space-y-6">
              <h3 className="text-sm font-semibold uppercase tracking-wider text-orange-500">Support</h3>
              <p className="text-gray-300 text-sm">Need help? Our support team is available to assist you.</p>
              <div className="flex items-center space-x-3 text-gray-300 bg-white/5 p-3 rounded-lg">
                <Mail className="w-5 h-5 text-orange-500" />
                <a href="mailto:<EMAIL>" className="text-sm hover:text-white transition-colors duration-200">
                  <EMAIL>
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Disclaimer section */}
        <div className="py-8 border-b border-white/10">
          <div className="bg-white/5 rounded-lg p-4 border border-white/10">
            <div className="flex items-start gap-3">
              <AlertCircle className="w-5 h-5 text-orange-500 flex-shrink-0 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-white mb-2">Risk Disclosure</h4>
                <p className="text-xs text-gray-400 leading-relaxed">
                  Trading financial markets carries substantial risk of loss. Content is educational only and not financial advice. Funded Horizon does not execute trades or manage funds. Services are provided through third parties. Past performance does not guarantee future results. Trade only with risk capital. Use of platform acknowledges acceptance of these risks.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom section with copyright and legal links */}
        <div className="py-6">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="text-gray-400 text-xs">
              © {new Date().getFullYear()} Funded Horizon. All rights reserved.
            </p>
            <div className="flex flex-wrap justify-center gap-6">
              <Link href="#" className="text-gray-400 hover:text-white text-xs flex items-center gap-1 transition-colors duration-200">
                <FileText className="w-3 h-3" />
                <span>Terms of Service</span>
              </Link>
              <Link href="#" className="text-gray-400 hover:text-white text-xs flex items-center gap-1 transition-colors duration-200">
                <Shield className="w-3 h-3" />
                <span>Privacy Policy</span>
              </Link>
              <Link href="#" className="text-gray-400 hover:text-white text-xs flex items-center gap-1 transition-colors duration-200">
                <ExternalLink className="w-3 h-3" />
                <span>Cookies</span>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
