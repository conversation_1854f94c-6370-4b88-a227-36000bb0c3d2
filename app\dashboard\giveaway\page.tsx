"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Gift, Search, DollarSign, Users, Award, CheckCircle } from "lucide-react"
import { toast } from "react-toastify"
import { getApiUrl, getEndpointPath, secureFetch } from "../../config/api"
import secureStorageAPI from '@/app/lib/secureStorage'

interface GiveawayEntry {
  id: number
  user_id: number
  order_id: number
  email: string
  account_size: string
  order_value: number
  created_at: string
  is_winner: boolean
  prize_amount: number | null
}

interface GiveawayApiResponse {
  total_entries: number
  total_eligible_orders: number
  total_prize_value: number
  entries: GiveawayEntry[]
}

export default function GiveawayPage() {
  const [entries, setEntries] = useState<GiveawayEntry[]>([])
  const [stats, setStats] = useState({
    total_entries: 0,
    total_eligible_orders: 0,
    total_prize_value: 0
  })
  const [search, setSearch] = useState("")
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchGiveawayData()
  }, [])

  const fetchGiveawayData = async () => {
    try {
      setIsLoading(true)
      const token = secureStorageAPI.getItem('access_token')
      
      if (!token) {
        console.error('No access token found')
        return
      }

      const response = await secureFetch('referral/giveaway/entries')
      
      if (response.ok) {
        const data = await response.json()
      setEntries(data.entries)
      setStats({
        total_entries: data.total_entries,
        total_eligible_orders: data.total_eligible_orders,
        total_prize_value: data.total_prize_value
      })
      } else {
        console.error('Failed to fetch giveaway data')
      }
    } catch (error) {
      console.error('Error fetching giveaway data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const filteredEntries = entries.filter(entry =>
    entry.order_id.toString().includes(search) ||
    entry.account_size.includes(search)
  )

  const totalWinners = entries.filter(e => e.is_winner).length
  const totalWinnerAmount = entries.reduce((sum, e) => sum + (e.prize_amount || 0), 0)

  return (
    <div className="min-h-screen relative bg-gradient-to-br from-blue-950 via-slate-900 to-orange-950 text-white overflow-x-hidden">
      {/* Animated background shapes */}
      <div className="pointer-events-none select-none absolute inset-0 z-0">
        <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-500/20 to-orange-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-tr from-orange-500/20 to-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute top-1/2 left-1/2 w-[600px] h-[600px] bg-gradient-to-r from-blue-500/10 to-orange-500/10 rounded-full blur-3xl animate-pulse delay-500 -translate-x-1/2 -translate-y-1/2" />
      </div>
      <div className="container mx-auto px-4 py-8 md:py-16 relative z-10">
        <div className="max-w-6xl mx-auto shadow-2xl rounded-3xl bg-gradient-to-br from-blue-900/80 via-slate-900/80 to-orange-900/80 border-2 border-blue-500/20 backdrop-blur-xl p-2 md:p-4">
          <div className="p-6 space-y-6">
            {/* Header */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                <h1 className="text-3xl font-bold text-slate-100 flex items-center gap-2"><Gift size={28}/> Giveaway Entries ({entries.length})</h1>
                <p className="text-slate-400 mt-1">Buy an account up to <span className="text-orange-400 font-bold">$50,000</span> and you are <span className="text-green-400 font-bold">automatically registered</span> in the giveaway. <span className="text-orange-400 font-semibold">Never miss this chance!</span></p>
              </div>
              <div className="flex flex-col gap-2 md:gap-0 md:flex-row md:items-center md:space-x-4">
                <Badge className="bg-blue-500/15 text-blue-400 border-blue-500/25"><Users size={14} className="mr-1" /> Total Registered: {stats.total_entries}</Badge>
                <Badge className="bg-green-500/15 text-green-400 border-green-500/25"><Award size={14} className="mr-1" /> Eligible Orders: {stats.total_eligible_orders}</Badge>
                <Badge className="bg-orange-500/15 text-orange-400 border-orange-500/25"><DollarSign size={14} className="mr-1" /> Total Prize: ${stats.total_prize_value}</Badge>
                <Badge className="bg-purple-500/15 text-purple-400 border-purple-500/25"><CheckCircle size={14} className="mr-1" /> Winners: {totalWinners} | Amount: ${totalWinnerAmount}</Badge>
              </div>
            </div>

            {/* Search Bar */}
            <div className="flex items-center gap-3 mb-4">
              <Search className="text-slate-400" size={18} />
              <Input
                placeholder="Search by order ID or account size..."
                value={search}
                onChange={e => setSearch(e.target.value)}
                className="bg-slate-700/50 border-slate-600 text-slate-100 max-w-md"
              />
            </div>

            {/* Entries Table */}
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-slate-100">Giveaway Entries ({filteredEntries.length})</CardTitle>
                <CardDescription className="text-slate-400">All users registered for the giveaway</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="text-center py-8 text-slate-400">Loading entries...</div>
                ) : filteredEntries.length === 0 ? (
                  <div className="text-center py-8 text-slate-400">No entries found</div>
                ) : (
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow className="border-slate-700">
                          <TableHead className="text-slate-300">FDH Order ID</TableHead>
                          <TableHead className="text-slate-300">Account Size</TableHead>
                          <TableHead className="text-slate-300">Order Value</TableHead>
                          <TableHead className="text-slate-300">Registered At</TableHead>
                          <TableHead className="text-slate-300">Winner</TableHead>
                          <TableHead className="text-slate-300">Prize Amount</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredEntries.map(entry => (
                          <TableRow key={entry.id} className="border-slate-700 hover:bg-slate-700/30">
                            <TableCell className="text-slate-200 font-mono text-sm">FDH-{entry.order_id}</TableCell>
                            <TableCell className="text-slate-200">${entry.account_size}</TableCell>
                            <TableCell className="text-slate-200">${entry.order_value.toLocaleString()}</TableCell>
                            <TableCell className="text-slate-400 text-xs">{new Date(entry.created_at).toLocaleString()}</TableCell>
                            <TableCell>
                              {entry.is_winner ? (
                                <Badge className="bg-green-500/15 text-green-400 border-green-500/25">Winner</Badge>
                              ) : (
                                <Badge className="bg-slate-500/15 text-slate-400 border-slate-500/25">No</Badge>
                              )}
                            </TableCell>
                            <TableCell className="text-orange-400 font-semibold">{entry.prize_amount ? `$${entry.prize_amount}` : '-'}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
} 