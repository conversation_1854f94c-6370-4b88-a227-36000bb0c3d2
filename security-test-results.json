[{"status": "FAIL", "test": "Security Headers", "message": "", "timestamp": "2025-06-27T20:19:06.999Z"}, {"status": "FAIL", "test": "CSRF Protection", "message": "", "timestamp": "2025-06-27T20:19:07.018Z"}, {"status": "FAIL", "test": "Rate Limiting", "message": "", "timestamp": "2025-06-27T20:19:07.147Z"}, {"status": "FAIL", "test": "Input Validation: SQL Injection", "message": "", "timestamp": "2025-06-27T20:19:07.217Z"}, {"status": "FAIL", "test": "Input Validation: XSS Attack", "message": "", "timestamp": "2025-06-27T20:19:07.230Z"}, {"status": "FAIL", "test": "Input Validation: Path Traversal", "message": "", "timestamp": "2025-06-27T20:19:07.234Z"}, {"status": "FAIL", "test": "Input Validation: Valid Input", "message": "", "timestamp": "2025-06-27T20:19:07.265Z"}, {"status": "FAIL", "test": "Authentication", "message": "", "timestamp": "2025-06-27T20:19:07.301Z"}, {"status": "FAIL", "test": "XSS Protection: <script>alert(\"xss\")...", "message": "", "timestamp": "2025-06-27T20:19:07.318Z"}, {"status": "FAIL", "test": "XSS Protection: javascript:alert(\"xs...", "message": "", "timestamp": "2025-06-27T20:19:07.333Z"}, {"status": "FAIL", "test": "XSS Protection: <img src=\"x\" onerror...", "message": "", "timestamp": "2025-06-27T20:19:07.349Z"}, {"status": "FAIL", "test": "XSS Protection: <iframe src=\"javascr...", "message": "", "timestamp": "2025-06-27T20:19:07.353Z"}, {"status": "FAIL", "test": "XSS Protection: <svg onload=\"alert('...", "message": "", "timestamp": "2025-06-27T20:19:07.384Z"}, {"status": "FAIL", "test": "SQL Injection Protection: ' OR '1'='1...", "message": "", "timestamp": "2025-06-27T20:19:07.401Z"}, {"status": "FAIL", "test": "SQL Injection Protection: '; DROP TABLE users;...", "message": "", "timestamp": "2025-06-27T20:19:07.413Z"}, {"status": "FAIL", "test": "SQL Injection Protection: ' UNION SELECT * FRO...", "message": "", "timestamp": "2025-06-27T20:19:07.419Z"}, {"status": "FAIL", "test": "SQL Injection Protection: admin'--...", "message": "", "timestamp": "2025-06-27T20:19:07.433Z"}, {"status": "FAIL", "test": "SQL Injection Protection: 1' OR '1' = '1' --...", "message": "", "timestamp": "2025-06-27T20:19:07.448Z"}, {"status": "FAIL", "test": "SQL Injection Protection: ' OR 1=1 --...", "message": "", "timestamp": "2025-06-27T20:19:07.451Z"}, {"status": "FAIL", "test": "SQL Injection Protection: ' OR 1=1#...", "message": "", "timestamp": "2025-06-27T20:19:07.464Z"}, {"status": "FAIL", "test": "SQL Injection Protection: ' OR 1=1/*...", "message": "", "timestamp": "2025-06-27T20:19:07.467Z"}, {"status": "FAIL", "test": "SQL Injection Protection: admin' OR '1'='1' --...", "message": "", "timestamp": "2025-06-27T20:19:07.483Z"}, {"status": "FAIL", "test": "SQL Injection Protection: '; INSERT INTO users...", "message": "", "timestamp": "2025-06-27T20:19:07.515Z"}, {"status": "FAIL", "test": "SQL Injection Protection: '; UPDATE users SET ...", "message": "", "timestamp": "2025-06-27T20:19:07.529Z"}, {"status": "FAIL", "test": "SQL Injection Protection: '; DELETE FROM users...", "message": "", "timestamp": "2025-06-27T20:19:07.532Z"}, {"status": "FAIL", "test": "SQL Injection Protection: ' UNION SELECT usern...", "message": "", "timestamp": "2025-06-27T20:19:07.535Z"}, {"status": "FAIL", "test": "SQL Injection Protection: ' UNION SELECT table...", "message": "", "timestamp": "2025-06-27T20:19:07.551Z"}, {"status": "FAIL", "test": "SQL Injection Protection: '; EXEC xp_cmdshell(...", "message": "", "timestamp": "2025-06-27T20:19:07.565Z"}, {"status": "FAIL", "test": "SQL Injection Protection: '; EXEC master..xp_c...", "message": "", "timestamp": "2025-06-27T20:19:07.568Z"}, {"status": "FAIL", "test": "SQL Injection Protection: '; SELECT LOAD_FILE(...", "message": "", "timestamp": "2025-06-27T20:19:07.578Z"}, {"status": "FAIL", "test": "SQL Injection Protection: '; SELECT INTO OUTFI...", "message": "", "timestamp": "2025-06-27T20:19:07.583Z"}, {"status": "FAIL", "test": "SQL Injection Protection: '; WAITFOR DELAY '00...", "message": "", "timestamp": "2025-06-27T20:19:07.586Z"}, {"status": "FAIL", "test": "SQL Injection Protection: '; BENCHMARK(1000000...", "message": "", "timestamp": "2025-06-27T20:19:07.598Z"}, {"status": "FAIL", "test": "File Upload Security: test.php", "message": "", "timestamp": "2025-06-27T20:19:07.602Z"}, {"status": "FAIL", "test": "File Upload Security: test.js", "message": "", "timestamp": "2025-06-27T20:19:07.616Z"}, {"status": "FAIL", "test": "File Upload Security: test.exe", "message": "", "timestamp": "2025-06-27T20:19:07.632Z"}, {"status": "FAIL", "test": "File Upload Security: test.bat", "message": "", "timestamp": "2025-06-27T20:19:07.635Z"}, {"status": "FAIL", "test": "File Upload Security: test.sh", "message": "", "timestamp": "2025-06-27T20:19:07.652Z"}, {"status": "FAIL", "test": "File Upload Security: test.py", "message": "", "timestamp": "2025-06-27T20:19:07.667Z"}, {"status": "FAIL", "test": "File Upload Security: test.rb", "message": "", "timestamp": "2025-06-27T20:19:07.719Z"}, {"status": "FAIL", "test": "File Upload Security: test.pl", "message": "", "timestamp": "2025-06-27T20:19:07.727Z"}, {"status": "FAIL", "test": "Path Traversal Protection: ../../../etc/passwd...", "message": "", "timestamp": "2025-06-27T20:19:07.748Z"}, {"status": "FAIL", "test": "Path Traversal Protection: ..\\..\\..\\windows\\sys...", "message": "", "timestamp": "2025-06-27T20:19:07.751Z"}, {"status": "FAIL", "test": "Path Traversal Protection: %2e%2e%2f%2e%2e%2f%2...", "message": "", "timestamp": "2025-06-27T20:19:07.763Z"}, {"status": "FAIL", "test": "Path Traversal Protection: ....//....//....//et...", "message": "", "timestamp": "2025-06-27T20:19:07.767Z"}, {"status": "FAIL", "test": "Path Traversal Protection: ..%252f..%252f..%252...", "message": "", "timestamp": "2025-06-27T20:19:07.769Z"}, {"status": "FAIL", "test": "Path Traversal Protection: ..%c0%af..%c0%af..%c...", "message": "", "timestamp": "2025-06-27T20:19:07.783Z"}, {"status": "FAIL", "test": "Content Security Policy", "message": "", "timestamp": "2025-06-27T20:19:07.801Z"}, {"status": "FAIL", "test": "Secure Storage", "message": "", "timestamp": "2025-06-27T20:19:07.829Z"}, {"status": "FAIL", "test": "API Endpoint: /api/auth/login", "message": "", "timestamp": "2025-06-27T20:19:07.835Z"}, {"status": "FAIL", "test": "API Endpoint: /api/auth/signup", "message": "", "timestamp": "2025-06-27T20:19:07.865Z"}, {"status": "FAIL", "test": "API Endpoint: /api/proxy/order/orders", "message": "", "timestamp": "2025-06-27T20:19:07.882Z"}, {"status": "FAIL", "test": "API Endpoint: /api/proxy/auth/users", "message": "", "timestamp": "2025-06-27T20:19:07.886Z"}, {"status": "FAIL", "test": "API Endpoint: /api/order/account_detail/123", "message": "", "timestamp": "2025-06-27T20:19:07.898Z"}]