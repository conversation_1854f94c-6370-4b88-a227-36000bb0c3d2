import { useState, useEffect } from 'react';

function useIsIPhone() {
  const [isIPhone, setIsIPhone] = useState(false);
  useEffect(() => {
    setIsIPhone(/iPhone|iPad|iPod/.test(navigator.userAgent));
  }, []);
  return isIPhone;
}

export default function CommunityCallToAction() {
  const isIPhone = useIsIPhone();
  return (
    <section className={`${isIPhone ? 'min-h-screen-ios' : 'min-h-screen'} ...`}>
      {/* ...existing section content... */}
    </section>
  )
} 