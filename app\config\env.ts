// Fake base URL for display purposes
const FAKE_BASE_URL = 'https://api.fundedhorizon.com';

export const getBaseUrl = () => {
  // Use non-public environment variable for backend URL
  let baseUrl = process.env.API_BASE_URL || 'https://fundedhorizon-back-e4285707ccdf.herokuapp.com/';
  
  // Ensure the base URL ends with a forward slash
  if (!baseUrl.endsWith('/')) {
    baseUrl += '/';
  }
  
  return baseUrl;
};

// Function to get fake base URL for display/logging purposes
export const getFakeBaseUrl = () => {
  return FAKE_BASE_URL;
}; 