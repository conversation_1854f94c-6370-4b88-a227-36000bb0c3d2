import { NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { sanitizeErrorResponse } from '@/app/config/api';

export async function GET(request: Request) {
  try {
    const headersList = headers();
    const authHeader = headersList.get('Authorization');

    if (!authHeader) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Use the proxy pattern instead of calling backend directly
    const response = await fetch('/api/proxy/ordors/order_ids', {
      method: 'GET',
      headers: {
        'Authorization': authHeader,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      const sanitizedErrorText = sanitizeErrorResponse(errorText);
      return NextResponse.json(
        { error: sanitizedErrorText },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 