import { Clock } from 'lucide-react';

export default function MovingSaleBanner() {
  return (
    <div className="w-full sticky top-0 z-[60] flex justify-center items-center bg-transparent">
      <div className="relative w-full max-w-screen-2xl mx-auto">
        <div className="glass-banner shadow-md border-b border-orange-400/40 overflow-hidden px-0.5">
          <div className="flex items-center gap-3 px-4 py-1 md:py-1.5">
            <span className="inline-flex items-center justify-center bg-gradient-to-tr from-orange-500 via-yellow-400 to-blue-600 rounded-full p-1.5 shadow-md">
              <Clock className="w-5 h-5 text-white animate-pulse" />
            </span>
            <div className="relative w-full overflow-hidden h-7 md:h-8">
              <div className="absolute whitespace-nowrap animate-marquee-pro text-sm md:text-lg font-semibold tracking-wide flex items-center gap-12 text-white">
                <span className="drop-shadow">🔥 <span className="font-bold text-yellow-300">Never Miss 70% OFF</span> on every account!  | Limited Time Offer! 🔥</span>
                <span className="drop-shadow">🔥 <span className="font-bold text-yellow-300">Never Miss 70% OFF</span> on every account!   | Limited Time Offer! 🔥</span>
              </div>
            </div>
          </div>
          <div className="absolute left-0 right-0 top-0 h-1 pointer-events-none">
            <div className="w-full h-full bg-gradient-to-r from-orange-400 via-yellow-300 to-blue-500 blur-md opacity-50 animate-glow" />
          </div>
        </div>
        <style jsx>{`
          .glass-banner {
            background: rgba(24, 24, 40, 0.82);
            backdrop-filter: blur(10px) saturate(1.2);
            border-radius: 0;
            box-shadow: 0 2px 16px 0 rgba(16, 24, 40, 0.10);
          }
          .animate-marquee-pro {
            animation: marquee-pro 18s linear infinite;
          }
          @keyframes marquee-pro {
            0% { left: 100%; }
            100% { left: -100%; }
          }
          .animate-glow {
            animation: glow 2.5s ease-in-out infinite alternate;
          }
          @keyframes glow {
            0% { opacity: 0.4; }
            100% { opacity: 1; }
          }
        `}</style>
      </div>
    </div>
  );
} 