import { NextResponse } from 'next/server';

interface ApiResponse {
  data?: any;
  message?: string;
  status?: number;
}

export const createSecureResponse = ({ data, message, status = 200 }: ApiResponse) => {
  // Sanitize the data to remove any sensitive information
  const sanitizeData = (obj: any): any => {
    if (Array.isArray(obj)) {
      return obj.map(sanitizeData);
    }
    if (obj && typeof obj === 'object') {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(obj)) {
        // Skip any properties that might contain sensitive URLs or internal data
        if (!key.toLowerCase().includes('url') && 
            !key.toLowerCase().includes('endpoint') && 
            !key.toLowerCase().includes('server')) {
          sanitized[key] = sanitizeData(value);
        }
      }
      return sanitized;
    }
    return obj;
  };

  const responseBody = {
    ...(data && { data: sanitizeData(data) }),
    ...(message && { message })
  };

  const headers = new Headers({
    'Content-Type': 'application/json',
    // Security headers
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    // Prevent caching of API responses
    'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0',
  });

  return new NextResponse(JSON.stringify(responseBody), {
    status,
    headers
  });
}; 