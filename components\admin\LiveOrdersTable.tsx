import { useState } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { OrderDetails, BaseOrderTableProps } from "./types"
import { BaseTable } from "./BaseTable"
import { Button } from "@/components/ui/button"
import { Eye, Activity } from "lucide-react"
import { Badge } from "@/components/ui/badge"

interface LiveOrdersTableProps extends BaseOrderTableProps {
  isMobile: boolean
}

export function LiveOrdersTable({
  orders,
  searchTerm,
  currentPage,
  itemsPerPage,
  onPageChange,
  onSearch,
  onRefresh,
  isRefreshing,
  onViewOrder,
  isMobile
}: LiveOrdersTableProps) {
  const filteredOrders = orders.filter(order =>
    Object.values(order).some(value =>
      value?.toString().toLowerCase().includes(searchTerm.toLowerCase())
    )
  )

  const paginatedOrders = filteredOrders.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  )

  return (
    <BaseTable
      data={orders}
      searchTerm={searchTerm}
      currentPage={currentPage}
      itemsPerPage={itemsPerPage}
      onPageChange={onPageChange}
      onSearch={onSearch}
      onRefresh={onRefresh}
      isRefreshing={isRefreshing}
      title="Live Orders"
    >
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Order ID</TableHead>
              <TableHead>User</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Account Type</TableHead>
              <TableHead>Balance</TableHead>
              {!isMobile && <TableHead>Created At</TableHead>}
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedOrders.map((order) => (
              <TableRow key={order.id}>
                <TableCell>{order.id}</TableCell>
                <TableCell>{order.user.name}</TableCell>
                <TableCell>{order.amount}</TableCell>
                <TableCell>{order.accountType}</TableCell>
                <TableCell>
                  <Badge 
                    variant={order.currentBalance && order.currentBalance > order.startingBalance ? "success" : "default"}
                    className="flex items-center gap-1"
                  >
                    <Activity className="h-3 w-3" />
                    ${order.currentBalance}
                  </Badge>
                </TableCell>
                {!isMobile && <TableCell>{order.createdAt}</TableCell>}
                <TableCell>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onViewOrder?.(order)}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </BaseTable>
  )
} 