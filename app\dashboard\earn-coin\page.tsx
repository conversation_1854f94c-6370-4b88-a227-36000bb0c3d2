"use client"

import { motion } from "framer-motion"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle, CardDescription } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Gift, Coins, Star, Video, Users, MessageCircle, Clock, ArrowRight } from "lucide-react"
import Link from "next/link"

export default function EarnCoinPage() {
  return (
    <div className="min-h-screen relative bg-gradient-to-br from-blue-950 via-slate-900 to-orange-950 text-white overflow-x-hidden">
      {/* Animated background shapes */}
      <div className="pointer-events-none select-none absolute inset-0 z-0">
        <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-500/20 to-orange-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-tr from-orange-500/20 to-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute top-1/2 left-1/2 w-[600px] h-[600px] bg-gradient-to-r from-blue-500/10 to-orange-500/10 rounded-full blur-3xl animate-pulse delay-500 -translate-x-1/2 -translate-y-1/2" />
      </div>
      <div className="container mx-auto px-4 py-8 md:py-16 relative z-10">
        <div className="max-w-4xl mx-auto shadow-2xl rounded-3xl bg-gradient-to-br from-blue-900/80 via-slate-900/80 to-orange-900/80 border-2 border-blue-500/20 backdrop-blur-xl p-2 md:p-4">
          <div className="p-6 space-y-8">
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex flex-col items-center text-center gap-2"
            >
              <h1 className="text-3xl font-bold text-slate-100 flex items-center gap-2"><Coins size={28}/> Earn Coins</h1>
              <p className="text-slate-400 max-w-2xl">Soon you'll be able to earn points by engaging with our platform and community. Complete real actions like leaving a review, submitting a video testimonial, or sharing your experience to unlock exclusive rewards. Stay tuned!</p>
            </motion.div>

            {/* Coming Soon Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <Card className="bg-slate-800/60 border-slate-700 relative overflow-hidden">
                  <CardHeader>
                    <CardTitle className="text-slate-100 flex items-center gap-2">
                      <Star size={20} />
                      Trustpilot Review
                    </CardTitle>
                    <CardDescription className="text-slate-400">Leave a review on Trustpilot and earn bonus points for your account.</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Badge className="bg-yellow-500/15 text-yellow-400 border-yellow-500/25">Coming Soon</Badge>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                <Card className="bg-slate-800/60 border-slate-700 relative overflow-hidden">
                  <CardHeader>
                    <CardTitle className="text-slate-100 flex items-center gap-2">
                      <Video size={20} />
                      Video Testimonial
                    </CardTitle>
                    <CardDescription className="text-slate-400">Submit a video review about your experience and earn extra points.</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Badge className="bg-yellow-500/15 text-yellow-400 border-yellow-500/25">Coming Soon</Badge>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <Card className="bg-slate-800/60 border-slate-700 relative overflow-hidden">
                  <CardHeader>
                    <CardTitle className="text-slate-100 flex items-center gap-2">
                      <MessageCircle size={20} />
                      Social Engagement
                    </CardTitle>
                    <CardDescription className="text-slate-400">Share your trading journey on social media and earn points for every post.</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Badge className="bg-yellow-500/15 text-yellow-400 border-yellow-500/25">Coming Soon</Badge>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <Link href="/dashboard/referal" className="block">
                  <Card className="bg-slate-800/60 border-slate-700 relative overflow-hidden hover:bg-slate-800/80 hover:border-blue-500/50 transition-all duration-300 cursor-pointer group">
                    <CardHeader>
                      <CardTitle className="text-slate-100 flex items-center gap-2 group-hover:text-blue-400 transition-colors">
                        <Users size={20} />
                        Refer a Friend
                        <ArrowRight size={16} className="ml-auto opacity-0 group-hover:opacity-100 transition-opacity" />
                      </CardTitle>
                      <CardDescription className="text-slate-400">Invite friends to join Funded Horizon and earn points for every successful referral.</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Badge className="bg-green-500/15 text-green-400 border-green-500/25">Available Now</Badge>
                    </CardContent>
                  </Card>
                </Link>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
              >
                <Card className="bg-slate-800/60 border-slate-700 relative overflow-hidden">
                  <CardHeader>
                    <CardTitle className="text-slate-100 flex items-center gap-2">
                      <Gift size={20} />
                      Redeem Rewards
                    </CardTitle>
                    <CardDescription className="text-slate-400">Use your earned points to unlock exclusive bonuses, discounts, and merchandise.</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Badge className="bg-yellow-500/15 text-yellow-400 border-yellow-500/25">Coming Soon</Badge>
                  </CardContent>
                </Card>
              </motion.div>
            </div>

            <div className="text-center mt-10 text-slate-400 text-sm">
              <Clock size={16} className="inline-block mr-2 text-yellow-400" />
              All earning features are launching soon. Stay tuned for updates!
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 