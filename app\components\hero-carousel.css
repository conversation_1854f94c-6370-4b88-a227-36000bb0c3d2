/* Custom styles for Swiper */

.swiper {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1;
}

.swiper-slide {
  display: flex;
  justify-content: center;
  align-items: center;
  background-position: center;
  background-size: cover;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.swiper-slide-active {
  opacity: 1;
}

/* Fix for slide overlap */
.swiper-wrapper {
  position: relative;
  z-index: 1;
}

/* Custom pagination bullets */
.swiper-pagination-bullet {
  width: 10px !important;
  height: 10px !important;
  background: rgba(255, 255, 255, 0.3) !important;
  opacity: 1 !important;
  transition: all 0.3s ease;
}

.swiper-pagination-bullet-active {
  background: rgba(249, 115, 22, 0.8) !important; /* orange-500 */
  transform: scale(1.2);
}

/* Hide default navigation arrows */
.swiper-button-next::after,
.swiper-button-prev::after {
  display: none;
}

/* Fade effect enhancements */
.swiper-fade .swiper-slide {
  transition-property: opacity;
  transition-duration: 0.5s !important;
  position: absolute;
  width: 100%;
  height: 100%;
}

.swiper-fade .swiper-slide-active {
  position: relative;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .swiper-button-next,
  .swiper-button-prev {
    transform: scale(0.8);
  }

  .swiper-pagination-bullet {
    width: 8px !important;
    height: 8px !important;
  }
}
