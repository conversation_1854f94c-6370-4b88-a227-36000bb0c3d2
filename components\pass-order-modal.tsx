"use client"

import { useState, useEffect } from "react"
import { Award, CheckCircle } from "lucide-react"
import { OrderDetails } from "@/types/order"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  <PERSON>alog<PERSON>ooter,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  <PERSON>alogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"

interface PassOrderModalProps {
  order: OrderDetails | any
  onPass: (orderId: string, profitAmount: number, notes: string) => Promise<void>
}

export function PassOrderModal({ order, onPass }: PassOrderModalProps) {
  const [open, setOpen] = useState(false)
  const [profitAmount, setProfitAmount] = useState<number>(0)
  const [notes, setNotes] = useState<string>("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [mounted, setMounted] = useState(false)

  // Handle client-side rendering
  useEffect(() => {
    setMounted(true)
  }, [])

  const handlePassOrder = async () => {
    if (!profitAmount) {
      alert("Please enter a profit amount")
      return
    }
    
    setIsSubmitting(true)
    try {
      await onPass(order.order_id, profitAmount, notes)
      setOpen(false)
      setProfitAmount(0)
      setNotes("")
    } catch (error) {
      console.error("Error passing order:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!mounted) {
    return null
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button 
          variant="outline" 
          size="sm"
          className="w-full md:w-auto bg-gradient-to-r from-green-900 to-green-700 hover:from-green-950 hover:to-green-800 text-white border-green-700 shadow-xl transition duration-300 rounded-xl"
        >
          Pass Order
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md bg-gradient-to-b from-gray-900 to-gray-950 border border-green-800/30 shadow-2xl rounded-xl p-4 overflow-y-auto max-h-[85vh]">
        <DialogHeader className="relative">
          <div className="absolute top-0 right-0 bg-green-900/30 px-2 py-0.5 rounded-lg text-[10px] text-green-300 font-mono backdrop-blur-sm border border-green-800/30">
            Order ID: {order.order_id}
          </div>
          <DialogTitle className="text-base font-bold text-white flex items-center gap-2">
            <div className="p-1.5 bg-green-900/50 rounded-lg shadow-lg backdrop-blur-sm">
              <Award className="w-4 h-4 text-green-300" />
            </div>
            Pass Trading Challenge
          </DialogTitle>
          <DialogDescription className="text-gray-400 text-xs mt-1">
            Confirm this trader has passed the trading challenge
          </DialogDescription>
        </DialogHeader>

        <div className="mt-4 space-y-4">
          <div className="space-y-2 bg-gray-800/30 p-3 rounded-xl border border-green-800/30 shadow-lg backdrop-blur-sm">
            <h3 className="text-xs font-bold text-white flex items-center gap-1.5">
              <CheckCircle className="w-3.5 h-3.5 text-green-400" />
              Pass Details
            </h3>
            
            <div className="space-y-3">
              <div>
                <Label className="text-gray-300 text-xs">Profit Amount ($)</Label>
                <Input 
                  type="number"
                  value={profitAmount || ''} 
                  onChange={(e) => setProfitAmount(Number(e.target.value))}
                  className="mt-1 bg-gray-700/30 border-green-700/50 text-white rounded-lg text-xs focus:ring-green-500 focus:border-green-500" 
                  placeholder="Enter profit amount"
                />
              </div>

              <div>
                <Label className="text-gray-300 text-xs">Notes (Optional)</Label>
                <Textarea 
                  value={notes} 
                  onChange={(e) => setNotes(e.target.value)}
                  className="mt-1 bg-gray-700/30 border-green-700/50 text-white rounded-lg text-xs focus:ring-green-500 focus:border-green-500" 
                  placeholder="Add any notes about this pass"
                  rows={3}
                />
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="mt-4">
          <Button 
            onClick={handlePassOrder}
            disabled={isSubmitting}
            className="w-full sm:w-auto bg-gradient-to-r from-green-900 to-green-700 hover:from-green-950 hover:to-green-800 text-white text-xs rounded-lg py-2 px-4 shadow-lg transition duration-300"
          >
            {isSubmitting ? "Processing..." : "Pass Order"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 