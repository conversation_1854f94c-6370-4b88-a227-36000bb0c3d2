import { useState } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { RunningOrder } from "./types"
import { BaseTable } from "./BaseTable"
import { Button } from "@/components/ui/button"
import { Co<PERSON>, Check } from "lucide-react"
import { toast } from "@/components/ui/use-toast"

interface RunningOrdersTableProps {
  orders: RunningOrder[]
  searchTerm: string
  currentPage: number
  itemsPerPage: number
  onPageChange: (page: number) => void
  onSearch: (e: React.ChangeEvent<HTMLInputElement>) => void
  onRefresh: () => void
  isRefreshing: boolean
  onFailOrder: (orderId: string) => void
}

export function RunningOrdersTable({
  orders,
  searchTerm,
  currentPage,
  itemsPerPage,
  onPageChange,
  onSearch,
  onRefresh,
  isRefreshing,
  onFailOrder
}: RunningOrdersTableProps) {
  const [copiedTxid, setCopiedTxid] = useState<string | null>(null)

  const filteredOrders = orders.filter(order =>
    Object.values(order).some(value =>
      value?.toString().toLowerCase().includes(searchTerm.toLowerCase())
    )
  )

  const paginatedOrders = filteredOrders.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  )

  const handleCopyTxid = (txid: string | undefined) => {
    if (txid) {
      navigator.clipboard.writeText(txid)
      setCopiedTxid(txid)
      toast({
        title: "Copied!",
        description: "Transaction ID copied to clipboard",
      })
      setTimeout(() => setCopiedTxid(null), 2000)
    }
  }

  const renderTxid = (txid: string | undefined) => {
    if (!txid) return "N/A"
    const isCopied = copiedTxid === txid
    return (
      <div className="flex items-center space-x-2">
        <span className="font-mono">{txid}</span>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleCopyTxid(txid)}
        >
          {isCopied ? (
            <Check className="h-4 w-4 text-green-500" />
          ) : (
            <Copy className="h-4 w-4" />
          )}
        </Button>
      </div>
    )
  }

  return (
    <BaseTable
      data={orders}
      searchTerm={searchTerm}
      currentPage={currentPage}
      itemsPerPage={itemsPerPage}
      onPageChange={onPageChange}
      onSearch={onSearch}
      onRefresh={onRefresh}
      isRefreshing={isRefreshing}
      title="Running Orders"
    >
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Order ID</TableHead>
              <TableHead>Platform Login</TableHead>
              <TableHead>Platform Password</TableHead>
              <TableHead>Server</TableHead>
              <TableHead>Session ID</TableHead>
              <TableHead>Terminal ID</TableHead>
              <TableHead>TXID</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedOrders.map((order) => (
              <TableRow key={order.id}>
                <TableCell>{order.order_id}</TableCell>
                <TableCell>{order.platform_login}</TableCell>
                <TableCell>{order.platform_password}</TableCell>
                <TableCell>{order.server}</TableCell>
                <TableCell>{order.session_id}</TableCell>
                <TableCell>{order.terminal_id}</TableCell>
                <TableCell>{renderTxid(order.txid)}</TableCell>
                <TableCell>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => onFailOrder(order.order_id)}
                  >
                    Fail Order
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </BaseTable>
  )
} 