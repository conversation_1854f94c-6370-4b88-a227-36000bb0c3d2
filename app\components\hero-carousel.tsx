'use client'

import { useEffect, useState } from 'react'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Autoplay, EffectFade, Navigation, Pagination } from 'swiper/modules'
import { Button } from '@/components/ui/button'
import { ChevronRight, ChevronLeft } from 'lucide-react'

// Import Swiper styles
import 'swiper/css'
import 'swiper/css/effect-fade'
import 'swiper/css/navigation'
import 'swiper/css/pagination'

// Define the slide data
const carouselSlides = [
  {
    id: 1,
    headline: 'Get Funded Fast',
    description: 'Pass our trading challenge and receive up to $2.5M in trading capital within 24 hours. No hidden fees, no time limits.',
    ctaText: 'Start Trading Now',
    ctaLink: '/signup',
    bgGradient: 'from-orange-500/20 to-blue-600/10',
  },
  {
    id: 2,
    headline: 'Track Your Performance in Real-Time',
    description: 'Monitor your trading metrics with our advanced dashboard. Get insights on drawdown, profit, and more to optimize your strategy.',
    ctaText: 'View Dashboard Demo',
    ctaLink: '/dashboard',
    bgGradient: 'from-blue-600/20 to-purple-500/10',
  },
  {
    id: 3,
    headline: 'Withdraw Profits Without Delay',
    description: 'Enjoy bi-weekly payouts with up to 90% profit splits. Our streamlined process ensures you get paid quickly and consistently.',
    ctaText: 'See Payout Structure',
    ctaLink: '/#pricing',
    bgGradient: 'from-green-500/20 to-blue-600/10',
  },
  {
    id: 4,
    headline: 'Trade with Confidence',
    description: 'Our risk management framework and 24/7 support team ensure you have everything you need to succeed in the markets.',
    ctaText: 'Explore Features',
    ctaLink: '/#features',
    bgGradient: 'from-purple-500/20 to-orange-500/10',
  }
]

export default function HeroCarousel() {
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    setIsLoaded(true)
  }, [])

  const handleNavigation = (path: string) => {
    // For hash links on the landing page
    if (path.startsWith('#')) {
      const element = document.querySelector(path)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
        return
      }
    }

    // Ensure the path starts with a forward slash
    const normalizedPath = path.startsWith('/') ? path : `/${path}`
    window.location.href = normalizedPath
  }

  return (
    <div className="w-full h-full relative">
      {isLoaded && (
        <Swiper
          modules={[Autoplay, EffectFade, Navigation, Pagination]}
          effect="fade"
          autoplay={{
            delay: 5000,
            disableOnInteraction: false,
          }}
          pagination={{
            clickable: true,
          }}
          navigation={{
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
          }}
          loop={true}
          speed={800}
          className="w-full h-full rounded-xl overflow-hidden"
        >
          {carouselSlides.map((slide) => (
            <SwiperSlide key={slide.id} className="w-full">
              <div className={`w-full h-full px-4 py-8 md:py-16 bg-gradient-to-br ${slide.bgGradient} rounded-xl backdrop-blur-sm border border-white/10 shadow-xl`}>
                <div className="max-w-3xl mx-auto text-center">
                  <h2 className="text-3xl md:text-5xl lg:text-6xl font-bold mb-4 md:mb-6 text-white">
                    {slide.headline}
                  </h2>
                  <p className="text-base md:text-xl text-gray-300 mb-8 md:mb-10">
                    {slide.description}
                  </p>
                  <Button
                    type="button"
                    onClick={() => handleNavigation(slide.ctaLink)}
                    className="px-6 py-3 md:px-8 md:py-4 bg-gradient-to-r from-orange-500 to-blue-600 text-white rounded-lg text-base md:text-lg font-semibold transition-all duration-300 hover:shadow-lg hover:shadow-orange-500/20"
                  >
                    {slide.ctaText}
                    <ChevronRight className="ml-2 w-5 h-5" />
                  </Button>
                </div>
              </div>
            </SwiperSlide>
          ))}

          {/* Custom Navigation Arrows */}
          <button
            type="button"
            aria-label="Previous slide"
            title="Previous slide"
            className="swiper-button-prev !w-10 !h-10 !bg-black/30 !backdrop-blur-md !rounded-full !text-white hover:!bg-black/50 transition-all duration-300 !left-4 after:!content-[''] flex items-center justify-center"
          >
            <ChevronLeft className="w-5 h-5" />
            <span className="sr-only">Previous slide</span>
          </button>
          <button
            type="button"
            aria-label="Next slide"
            title="Next slide"
            className="swiper-button-next !w-10 !h-10 !bg-black/30 !backdrop-blur-md !rounded-full !text-white hover:!bg-black/50 transition-all duration-300 !right-4 after:!content-[''] flex items-center justify-center"
          >
            <ChevronRight className="w-5 h-5" />
            <span className="sr-only">Next slide</span>
          </button>
        </Swiper>
      )}
    </div>
  )
}
