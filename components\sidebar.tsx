"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { cn } from "@/lib/utils"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { 
  LayoutDashboard, 
  Users, 
  FileCheck, 
  CreditCard, 
  LogOut, 
  Menu, 
  ShoppingCart,
  Settings,
  User,
  History,
  BookOpen,
  BarChart3,
  Shield,
  Bell,
  HelpCircle,
  Award,
  Sparkles,
  Zap,
  TrendingUp,
  Coins,
  Gift
} from "lucide-react"
import { motion } from "framer-motion"
import secureStorageAPI from '@/app/lib/secureStorage'

const dashboardItems = [
  { 
    icon: LayoutDashboard, 
    label: "Dashboard", 
    href: "/dashboard",
    description: "Trading overview and analytics",
    gradient: "from-blue-500 to-blue-600"
  },
  { 
    icon: ShoppingCart, 
    label: "Buy Challenge", 
    href: "/dashboard/buy",
    description: "Purchase trading challenges",
    gradient: "from-orange-500 to-orange-600"
  },
  { 
    icon: BarChart3, 
    label: "Order History", 
    href: "/dashboard/order-history",
    description: "View all your trading orders",
    gradient: "from-blue-500 to-blue-600"
  },
  { 
    icon: Users, 
    label: "Referrals", 
    href: "/dashboard/referal",
    description: "Manage your referral network",
    gradient: "from-blue-500 to-blue-600"
  },
  { 
    icon: Coins, 
    label: "Earn Coin", 
    href: "/dashboard/earn-coin",
    description: "Earn and manage your coins",
    gradient: "from-orange-500 to-orange-600"
  },
  { 
    icon: Gift, 
    label: "Giveaway", 
    href: "/dashboard/giveaway",
    description: "Participate in giveaways",
    gradient: "from-blue-500 to-blue-600"
  },
  { 
    icon: FileCheck, 
    label: "KYC Verification", 
    href: "/dashboard/kyc",
    description: "Complete identity verification",
    gradient: "from-orange-500 to-orange-600"
  },
  { 
    icon: CreditCard, 
    label: "Withdrawals", 
    href: "/dashboard/withdraw",
    description: "Withdraw your profits",
    gradient: "from-blue-500 to-blue-600"
  },
  { 
    icon: User, 
    label: "Profile", 
    href: "/dashboard/profile",
    description: "Manage your account settings",
    gradient: "from-orange-500 to-orange-600"
  },
  { 
    icon: BookOpen, 
    label: "Rules & Guidelines", 
    href: "/dashboard/rules",
    description: "Trading rules and policies",
    gradient: "from-blue-500 to-blue-600"
  }
]

const adminItems = [
  {
    icon: Settings,
    label: "Admin Portal",
    href: "/adminportal",
    description: "Administrative dashboard",
    gradient: "from-orange-500 to-orange-600"
  }
]

export function Sidebar() {
  const pathname = usePathname()
  const router = useRouter()
  const [isMobile, setIsMobile] = useState(false)
  const [isOpen, setIsOpen] = useState(false)
  const [userInfo, setUserInfo] = useState<any>(null)
  const [isInitialized, setIsInitialized] = useState(false)
  const navScrollRef = useRef<HTMLDivElement>(null)

  const isAdminPortal = pathname?.startsWith('/adminportal') || false
  const sidebarItems = isAdminPortal ? adminItems : dashboardItems
  const sidebarWidth = isAdminPortal ? 'w-64' : 'w-80'

  // Helper function to safely compare pathname
  const isActivePath = (href: string) => pathname === href

  useEffect(() => {
    const checkMobile = () => {
      const newIsMobile = window.innerWidth < 768
      setIsMobile(prev => prev !== newIsMobile ? newIsMobile : prev)
    }

    checkMobile()
    window.addEventListener("resize", checkMobile)
    return () => window.removeEventListener("resize", checkMobile)
  }, [])

  useEffect(() => {
    // Get user info from localStorage or API only once
    if (!isInitialized) {
      const token = secureStorageAPI.getItem('access_token')
      if (token) {
        // You can fetch user info here if needed
        setUserInfo({ name: "Trader", email: "<EMAIL>" })
      }
      setIsInitialized(true)
    }
  }, [isInitialized])

  const handleLogout = useCallback(() => {
    secureStorageAPI.secureLogout()
    router.push('/sigin')
  }, [router])

  const SidebarContent = () => {
    return (
      <div className="flex flex-col h-full bg-gradient-to-b from-slate-900 via-slate-800 to-slate-900 relative overflow-hidden">
        {/* Animated Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-500/5 via-orange-500/5 to-blue-500/5"></div>
          <div className="absolute top-20 left-20 w-32 h-32 bg-blue-500/10 rounded-full blur-2xl animate-pulse"></div>
          <div className="absolute bottom-20 right-20 w-40 h-40 bg-orange-500/10 rounded-full blur-2xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-gradient-to-r from-blue-500/5 to-orange-500/5 rounded-full blur-3xl animate-pulse delay-500"></div>
        </div>

        {/* Header - Reduced padding */}
        <div className="relative p-4 border-b border-slate-700/50">
          <Link href={isAdminPortal ? "/adminportal" : "/dashboard"} className="flex flex-col items-center gap-4 group">
            <Image 
              src="/logo.svg" 
              alt="Funded Horizon Logo" 
              width={160} 
              height={40}
              className="h-auto"
            />
          </Link>
        </div>

        {/* Navigation - Optimized for no scrolling */}
        <nav
          ref={navScrollRef}
          className="flex-1 p-3 space-y-0.5 overflow-y-auto scrollbar-thin scrollbar-thumb-blue-500 scrollbar-track-orange-100"
          style={{ scrollBehavior: 'smooth' }}
        >
          {sidebarItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className="relative block group"
              onClick={(e) => {
                // Prevent auto-scroll to top when clicking navigation items
                e.preventDefault()
                router.push(item.href)
              }}
            >
              <div
                className={cn(
                  "w-full flex items-center gap-2 px-2 py-1.5 rounded-lg transition-all duration-300 relative overflow-hidden backdrop-blur-sm",
                  isActivePath(item.href)
                    ? "bg-gradient-to-r from-blue-600/20 via-orange-600/20 to-blue-600/20 text-blue-100 border border-blue-500/30 shadow-lg shadow-blue-500/10 transform scale-[1.02]"
                    : "text-slate-300 hover:bg-gradient-to-r hover:from-slate-700/50 hover:to-slate-600/50 hover:text-slate-100 hover:border-slate-600/30 border border-transparent hover:transform hover:scale-[1.02]"
                )}
              >
                {/* Background gradient on hover */}
                <div className={cn(
                  "absolute inset-0 bg-gradient-to-r opacity-0 transition-opacity duration-300 group-hover:opacity-100",
                  `from-${item.gradient.split('-')[1]}-500/10 to-${item.gradient.split('-')[3]}-500/10`
                )} />
                {/* Icon with gradient background */}
                <div className={cn(
                  "relative z-10 p-1 rounded-md transition-all duration-300",
                  isActivePath(item.href)
                    ? `bg-gradient-to-br ${item.gradient} shadow-lg`
                    : `bg-slate-700/50 group-hover:bg-gradient-to-br group-hover:${item.gradient} group-hover:shadow-lg`
                )}>
                  <item.icon className={cn(
                    "h-3.5 w-3.5 transition-all duration-300",
                    isActivePath(item.href)
                      ? "text-white" 
                      : "text-slate-400 group-hover:text-white"
                  )} />
                </div>
                <div className="flex-1 relative z-10 min-w-0">
                  <span className="font-medium text-xs tracking-wide truncate">{item.label}</span>
                </div>
                {isActivePath(item.href) && (
                  <motion.div 
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="absolute right-2 w-1 h-3 rounded-full bg-gradient-to-b from-blue-400 to-orange-400 shadow-lg"
                  />
                )}
              </div>
            </Link>
          ))}
        </nav>

        {/* Footer - Reduced padding and font sizes */}
        <div className="relative p-2 border-t border-slate-700/50 space-y-1.5">
          {/* Quick Stats - Compact layout */}
          <div className="grid grid-cols-2 gap-1.5">
            <div className="p-1.5 rounded-lg bg-gradient-to-r from-blue-500/10 to-blue-600/10 border border-blue-500/20">
              <div className="flex items-center gap-1">
                <TrendingUp size={10} className="text-blue-400" />
                <span className="text-xs text-blue-400 font-medium">+12.5%</span>
              </div>
              <p className="text-xs text-slate-400 mt-0.5">This Week</p>
            </div>
            <div className="p-1.5 rounded-lg bg-gradient-to-r from-orange-500/10 to-orange-600/10 border border-orange-500/20">
              <div className="flex items-center gap-1">
                <Zap size={10} className="text-orange-400" />
                <span className="text-xs text-orange-400 font-medium">24/7</span>
              </div>
              <p className="text-xs text-slate-400 mt-0.5">Support</p>
            </div>
          </div>

          {/* Logout */}
          <button
            onClick={handleLogout}
            className="w-full flex items-center gap-2 px-2 py-1.5 text-left text-slate-400 hover:bg-gradient-to-r hover:from-orange-500/10 hover:to-orange-600/10 hover:text-orange-400 rounded-lg transition-all duration-300 group border border-transparent hover:border-orange-500/20"
          >
            <div className="p-0.5 rounded-lg bg-gradient-to-br from-orange-500/15 to-orange-600/15 group-hover:from-orange-500/25 group-hover:to-orange-600/25 transition-all duration-300">
              <LogOut size={12} className="group-hover:rotate-12 transition-transform duration-300" />
            </div>
            <span className="font-medium text-xs">Sign Out</span>
          </button>
        </div>
      </div>
    )
  }

  if (isMobile) {
    return (
      <>
        {/* Mobile Menu Button */}
        <Button
          variant="ghost"
          size="icon"
          className="fixed top-4 left-4 z-50 md:hidden bg-slate-800/90 backdrop-blur-sm border border-slate-700/50 hover:bg-slate-700/90 shadow-lg"
          onClick={() => setIsOpen(true)}
        >
          <Menu className="h-5 w-5 text-slate-200" />
        </Button>

        {/* Mobile Drawer */}
        <Sheet open={isOpen} onOpenChange={setIsOpen}>
          <SheetContent side="left" className={`${sidebarWidth} p-0 bg-slate-900 border-r border-slate-700/50`}>
            <SidebarContent />
          </SheetContent>
        </Sheet>

        {/* Desktop Sidebar */}
        <div className={`hidden md:block fixed inset-y-0 left-0 ${sidebarWidth} bg-slate-900 border-r border-slate-700/50 shadow-2xl`}>
          <SidebarContent />
        </div>
      </>
    )
  }

  return (
    <div className={`hidden md:block fixed left-0 top-0 h-screen ${sidebarWidth} bg-slate-900 text-slate-200 border-r border-slate-700/50 shadow-2xl`}>
      <SidebarContent />
    </div>
  )
}
