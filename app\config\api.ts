import { getBaseUrl } from './env';
import secureStorageAPI from '../lib/secureStorage';

// Fake base URL for display purposes (to hide real backend URL)
const FAKE_BASE_URL = 'https://api.fundedhorizon.com';

// API Configuration
export const API_CONFIG = {
  endpoints: {
    login: 'auth/login',
    signup: 'auth/signup',
    adminLogin: 'admin/login',
    order: 'ordors',
    orderIds: 'ordors/order_ids',
    accountDetails: 'ordors/account_detail',
    failOrder: 'ordors/fail_order',
    failedOrders: 'ordors/failed_orders',
    myfxbookFetch: 'myfxbook/fetch_account_details',
    // Admin endpoints (all should use 'ordors/' prefix)
    runningOrders: 'ordors/running_orders',
    completedOrders: 'ordors/completed_orders',
    orders: 'ordors/orders',
    passedOrders: 'ordors/passed_orders',
    stageTwoOrders: 'ordors/stage_two_orders',
    liveOrders: 'ordors/live_orders',
    orderStats: 'ordors/order_stats',
    userStats: 'ordors/user_stats',
    users: 'auth/users',
    'verify-2fa': 'admin/verify-2fa'
  } 
} as const;

// Helper function to get only the endpoint path (for secure logging)
export const getEndpointPath = (endpoint: keyof typeof API_CONFIG.endpoints | string): string => {
  if (typeof endpoint === 'string') {
    return endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  }
  return `/${API_CONFIG.endpoints[endpoint as keyof typeof API_CONFIG.endpoints]}`;
};

// Helper function to get fake API URL for display
export const getFakeApiUrl = (endpoint: keyof typeof API_CONFIG.endpoints | string): string => {
  if (typeof endpoint === 'string') {
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
    return `${FAKE_BASE_URL}/${cleanEndpoint}`;
  }
  return `${FAKE_BASE_URL}/${API_CONFIG.endpoints[endpoint as keyof typeof API_CONFIG.endpoints]}`;
};

// Helper function to get real API URL (for actual requests)
export const getApiUrl = (endpoint: keyof typeof API_CONFIG.endpoints | string): string => {
  // Use proxy instead of direct backend URL
  if (typeof endpoint === 'string') {
    // Remove leading slash if present in the endpoint
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
    return `/api/proxy/${cleanEndpoint}`;
  }
  return `/api/proxy/${API_CONFIG.endpoints[endpoint as keyof typeof API_CONFIG.endpoints]}`;
};

// Function to sanitize error responses and replace real URLs with fake ones
export const sanitizeErrorResponse = (responseText: string): string => {
  try {
    // Try to parse as JSON first
    const jsonData = JSON.parse(responseText);
    
    // Recursively replace URLs in the JSON object
    const sanitizeObject = (obj: any): any => {
      if (typeof obj === 'string') {
        // Replace real base URL with fake one in strings
        let sanitized = obj;
        
        // Replace various forms of the real backend URL
        sanitized = sanitized.replace(
          /https:\/\/fundedhorizon-back-e4285707ccdf\.herokuapp\.com/g,
          FAKE_BASE_URL
        );
        
        // Also replace any other variations that might exist
        sanitized = sanitized.replace(
          /fundedhorizon-back-e4285707ccdf\.herokuapp\.com/g,
          'api.fundedhorizon.com'
        );
        
        // Replace any localhost references (for development)
        sanitized = sanitized.replace(
          /http:\/\/localhost:\d+/g,
          FAKE_BASE_URL
        );
        
        return sanitized;
      } else if (Array.isArray(obj)) {
        return obj.map(sanitizeObject);
      } else if (obj && typeof obj === 'object') {
        const sanitized: any = {};
        for (const [key, value] of Object.entries(obj)) {
          sanitized[key] = sanitizeObject(value);
        }
        return sanitized;
      }
      return obj;
    };
    
    const sanitizedData = sanitizeObject(jsonData);
    return JSON.stringify(sanitizedData);
  } catch {
    // If not JSON, treat as plain text and replace URLs
    let sanitized = responseText;
    
    // Replace various forms of the real backend URL
    sanitized = sanitized.replace(
      /https:\/\/fundedhorizon-back-e4285707ccdf\.herokuapp\.com/g,
      FAKE_BASE_URL
    );
    
    // Also replace any other variations that might exist
    sanitized = sanitized.replace(
      /fundedhorizon-back-e4285707ccdf\.herokuapp\.com/g,
      'api.fundedhorizon.com'
    );
    
    // Replace any localhost references (for development)
    sanitized = sanitized.replace(
      /http:\/\/localhost:\d+/g,
      FAKE_BASE_URL
    );
    
    return sanitized;
  }
};

// Secure fetch functions
export const secureFetch = async (endpoint: string, options: RequestInit = {}) => {
  const token = secureStorageAPI.getItem('access_token');

  // Don't set Content-Type for FormData - let the browser set it with boundary
  const isFormData = options.body instanceof FormData;

  const headers: Record<string, string> = {
    ...options.headers as Record<string, string>,
    ...(token && { 'Authorization': `Bearer ${token}` })
  };

  // Only set Content-Type for non-FormData requests
  if (!isFormData) {
    headers['Content-Type'] = 'application/json';
  }

  const realApiEndpoint = getApiUrl(endpoint);
  const fakeApiUrl = getFakeApiUrl(endpoint);

  // Log only the fake URL for security (never show real backend URL)
  if (process.env.NODE_ENV !== 'production') {
    // eslint-disable-next-line no-console
    console.log('API Request:', fakeApiUrl);
  }

  try {
    const response = await fetch(realApiEndpoint, {
      ...options,
      headers
    });

    if (!response.ok) {
      // Get the error response text and sanitize it
      const errorText = await response.text();
      const sanitizedErrorText = sanitizeErrorResponse(errorText);
      
      // Log only the fake URL and status, not the real URL
      console.error(`API request failed for ${fakeApiUrl}: ${response.status} ${response.statusText}`);
      
      // Create a new response with sanitized error text
      const errorResponse = new Response(sanitizedErrorText, {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers
      });
      
      throw new Error(`API request failed: ${response.status}`);
    }

    return response;
  } catch (error) {
    console.error(`API request error for ${fakeApiUrl}:`, error);
    throw error;
  }
};

// Direct backend fetch (for server-side only)
export const backendFetch = async (path: string, options: RequestInit = {}) => {
  const baseUrl = getBaseUrl();
  const response = await fetch(`${baseUrl}${path}`, options);

  if (!response.ok) {
    throw new Error('Backend request failed');
  }

  return response;
}; 