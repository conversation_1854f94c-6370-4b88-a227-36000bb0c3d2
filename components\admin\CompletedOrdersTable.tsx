import { useState } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { OrderDetails, BaseOrderTableProps } from "./types"
import { BaseTable } from "./BaseTable"
import { Button } from "@/components/ui/button"
import { Eye, Copy, Check } from "lucide-react"
import { toast } from "@/components/ui/use-toast"

interface CompletedOrdersTableProps extends BaseOrderTableProps {
  isMobile: boolean
}

export function CompletedOrdersTable({
  orders,
  searchTerm,
  currentPage,
  itemsPerPage,
  onPageChange,
  onSearch,
  onRefresh,
  isRefreshing,
  onViewOrder,
  isMobile
}: CompletedOrdersTableProps) {
  const [copiedTxid, setCopiedTxid] = useState<string | null>(null)

  const filteredOrders = orders.filter(order =>
    Object.values(order).some(value =>
      value?.toString().toLowerCase().includes(searchTerm.toLowerCase())
    )
  )

  const paginatedOrders = filteredOrders.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  )

  const handleCopyTxid = (txid: string | undefined) => {
    if (txid) {
      navigator.clipboard.writeText(txid)
      setCopiedTxid(txid)
      toast({
        title: "Copied!",
        description: "Transaction ID copied to clipboard",
      })
      setTimeout(() => setCopiedTxid(null), 2000)
    }
  }

  const renderTxid = (txid: string | undefined) => {
    if (!txid) return "N/A"
    const isCopied = copiedTxid === txid
    return (
      <div className="flex items-center space-x-2">
        <span className="font-mono">{txid}</span>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleCopyTxid(txid)}
        >
          {isCopied ? (
            <Check className="h-4 w-4 text-green-500" />
          ) : (
            <Copy className="h-4 w-4" />
          )}
        </Button>
      </div>
    )
  }

  return (
    <BaseTable
      data={orders}
      searchTerm={searchTerm}
      currentPage={currentPage}
      itemsPerPage={itemsPerPage}
      onPageChange={onPageChange}
      onSearch={onSearch}
      onRefresh={onRefresh}
      isRefreshing={isRefreshing}
      title="Completed Orders"
    >
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Order ID</TableHead>
              <TableHead>User</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Account Type</TableHead>
              <TableHead>Platform</TableHead>
              <TableHead>TXID</TableHead>
              {!isMobile && <TableHead>Created At</TableHead>}
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedOrders.map((order) => (
              <TableRow key={order.id}>
                <TableCell>{order.id}</TableCell>
                <TableCell>{order.user.name}</TableCell>
                <TableCell>{order.amount}</TableCell>
                <TableCell>{order.accountType}</TableCell>
                <TableCell>{order.platformType}</TableCell>
                <TableCell>{renderTxid(order.txid)}</TableCell>
                {!isMobile && <TableCell>{order.createdAt}</TableCell>}
                <TableCell>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onViewOrder?.(order)}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </BaseTable>
  )
} 