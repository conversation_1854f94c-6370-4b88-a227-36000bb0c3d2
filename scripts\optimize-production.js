#!/usr/bin/env node

/**
 * Production Optimization Script
 * This script helps optimize the production build for Heroku deployment
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Optimizing for production...');

// Set production environment variables
process.env.NODE_ENV = 'production';
process.env.NEXT_TELEMETRY_DISABLED = '1';

// Create a production-specific .env file if it doesn't exist
const envPath = path.join(process.cwd(), '.env.production');
const envContent = `# Production Environment Variables
NODE_ENV=production
ALLOWED_ORIGINS=https://fundedhorizon.com,https://www.fundedhorizon.com,https://fhhou-866cad755156.herokuapp.com
SECURITY_STRICT_MODE=false
NEXT_PUBLIC_API_URL=https://fundedhorizon-back-e4285707ccdf.herokuapp.com
NEXT_TELEMETRY_DISABLED=1
`;

if (!fs.existsSync(envPath)) {
  fs.writeFileSync(envPath, envContent);
  console.log('✅ Created .env.production file');
}

// Optimize package.json scripts for production
const packagePath = path.join(process.cwd(), 'package.json');
if (fs.existsSync(packagePath)) {
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  
  // Add production-specific scripts
  packageJson.scripts = {
    ...packageJson.scripts,
    'build:prod': 'NODE_ENV=production next build',
    'start:prod': 'NODE_ENV=production next start',
    'optimize': 'node scripts/optimize-production.js'
  };
  
  fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
  console.log('✅ Updated package.json with production scripts');
}

console.log('🚀 Production optimization complete!');
console.log('');
console.log('Next steps:');
console.log('1. Run: npm run build:prod');
console.log('2. Deploy to Heroku');
console.log('3. Set environment variables in Heroku dashboard');
console.log('');
console.log('Environment variables to set in Heroku:');
console.log('- NODE_ENV=production');
console.log('- ALLOWED_ORIGINS=https://fundedhorizon.com,https://www.fundedhorizon.com,https://fhhou-866cad755156.herokuapp.com');
console.log('- SECURITY_STRICT_MODE=false');
console.log('- NEXT_TELEMETRY_DISABLED=1'); 