import { NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { getApiUrl, sanitizeErrorResponse } from '@/app/config/api';

export async function POST(request: Request) {
  try {
    const headersList = headers();
    const authHeader = headersList.get('Authorization');

    if (!authHeader) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const formData = await request.formData();
    const session = formData.get('session');
    const accountId = formData.get('account_id');

    console.log('MyFXBook fetch request:', { session, accountId });

    if (!session || !accountId) {
      return NextResponse.json(
        { error: 'Session and account_id are required' },
        { status: 400 }
      );
    }

    const response = await fetch(getApiUrl('myfxbook/fetch_account_details'), {
      method: 'POST',
      headers: {
        'Authorization': authHeader,
      },
      body: formData,
    });

    if (!response.ok) {
      const errorText = await response.text();
      const sanitizedErrorText = sanitizeErrorResponse(errorText);
      return NextResponse.json(
        { error: sanitizedErrorText },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log('MyFXBook response:', data);

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in MyFXBook fetch API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
