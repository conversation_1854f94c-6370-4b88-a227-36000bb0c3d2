import type { Metadata, Viewport } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import MetaPixel from './components/MetaPixel'
import { ToastProvider } from '@/components/providers/toast-provider'
import { Suspense } from 'react'
// import DevToolsPasswordPrompt from './components/DevToolsPasswordPrompt'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Funded Horizon',
  description: 'Funded Horizon - Your Path to Financial Success',
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon.ico',
    apple: '/favicon.ico',
  },
  other: {
    'mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-status-bar-style': 'default',
    'apple-mobile-web-app-title': 'Funded Horizon',
    'format-detection': 'telephone=no',
    'viewport-fit': 'cover',
  }
}

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  minimumScale: 1,
  viewportFit: 'cover',
  userScalable: false,
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Funded Horizon" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="viewport-fit" content="cover" />
        <link rel="preload" href="/logo.svg" as="image" type="image/svg+xml" />
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/favicon.ico" type="image/x-icon" />
        <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />
        <link rel="apple-touch-icon" href="/favicon.ico" />
        <style dangerouslySetInnerHTML={{
          __html: `
            /* iPhone-specific fixes */
            @supports (-webkit-touch-callout: none) {
              .min-h-screen {
                min-height: -webkit-fill-available;
              }
              
              body {
                min-height: -webkit-fill-available;
              }
            }
            
            /* Prevent zoom on input focus for iOS */
            @media screen and (max-width: 768px) {
              input, select, textarea {
                font-size: 16px !important;
              }
            }
            
            /* Improve touch scrolling on iOS */
            body {
              -webkit-overflow-scrolling: touch;
            }
            
            /* Fix for iOS Safari flexbox issues */
            .flex {
              display: -webkit-box;
              display: -webkit-flex;
              display: -ms-flexbox;
              display: flex;
            }
            
            /* Fix for iOS Safari grid issues */
            .grid {
              display: -webkit-box;
              display: -webkit-flex;
              display: -ms-flexbox;
              display: grid;
            }
            
            /* Optimize animations for mobile */
            @media (max-width: 768px) {
              .animate-spin {
                animation-duration: 2s;
              }
              
              .animate-pulse {
                animation-duration: 3s;
              }
            }
            
            /* Reduce motion for better performance */
            @media (prefers-reduced-motion: reduce) {
              *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
              }
            }
          `
        }} />
      </head>
      <body className={inter.className} suppressHydrationWarning>
        <script dangerouslySetInnerHTML={{
          __html: `
            function setVh() {
              var vh = window.innerHeight * 0.01;
              document.documentElement.style.setProperty('--vh', vh + 'px');
            }

            function optimizeForIPhone() {
              var isIPhone = /iPhone|iPad|iPod/.test(navigator.userAgent);
              if (isIPhone) {
                // Apply iPhone optimizations immediately
                document.documentElement.setAttribute('data-disable-heavy-components', '1');
                document.documentElement.setAttribute('data-reduce-motion', '1');
                document.documentElement.style.setProperty('--animation-duration', '0.3s');

                // Disable complex CSS animations
                var style = document.createElement('style');
                style.textContent = \`
                  @media (max-width: 768px) {
                    * { animation-duration: 0.3s !important; }
                    .animate-spin { animation: none !important; }
                    .animate-pulse { animation: none !important; }
                  }
                \`;
                document.head.appendChild(style);
              }
            }

            setVh();
            optimizeForIPhone();
            window.addEventListener('resize', setVh);
            window.addEventListener('orientationchange', setVh);
          `
        }} />
        <Suspense fallback={null}>
          {/* <DevToolsPasswordPrompt /> */}
        </Suspense>
        <Suspense fallback={null}>
          <MetaPixel />
        </Suspense>
        <Suspense fallback={
          <div className="min-h-screen flex items-center justify-center bg-[#0A0F1C]">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500"></div>
          </div>
        }>
          {children}
        </Suspense>
        <Suspense fallback={null}>
          <ToastProvider />
        </Suspense>
      </body>
    </html>
  )
}
