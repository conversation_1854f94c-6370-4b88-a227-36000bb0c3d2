import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';

// Security configuration
export const SECURITY_CONFIG = {
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION: 15 * 60 * 1000, // 15 minutes
  SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24 hours
  PASSWORD_MIN_LENGTH: 8,
  PASSWORD_REQUIREMENTS: {
    UPPERCASE: true,
    LOWERCASE: true,
    NUMBERS: true,
    SPECIAL_CHARS: true,
  },
  ALLOWED_FILE_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  RATE_LIMIT_WINDOW: 60 * 1000, // 1 minute
  RATE_LIMIT_MAX_REQUESTS: 100,
};

// Input validation and sanitization
export class InputValidator {
  static sanitizeString(input: string): string {
    if (typeof input !== 'string') return '';
    
    // Remove null bytes and control characters
    let sanitized = input.replace(/[\x00-\x1F\x7F]/g, '');
    
    // HTML entity encoding for XSS prevention
    sanitized = sanitized
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
    
    return sanitized.trim();
  }

  static validateEmail(email: string): boolean {
    if (!email || typeof email !== 'string') return false;
    
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email.toLowerCase());
  }

  static validatePassword(password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!password || typeof password !== 'string') {
      errors.push('Password is required');
      return { isValid: false, errors };
    }

    if (password.length < SECURITY_CONFIG.PASSWORD_MIN_LENGTH) {
      errors.push(`Password must be at least ${SECURITY_CONFIG.PASSWORD_MIN_LENGTH} characters`);
    }

    if (SECURITY_CONFIG.PASSWORD_REQUIREMENTS.UPPERCASE && !/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (SECURITY_CONFIG.PASSWORD_REQUIREMENTS.LOWERCASE && !/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (SECURITY_CONFIG.PASSWORD_REQUIREMENTS.NUMBERS && !/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    if (SECURITY_CONFIG.PASSWORD_REQUIREMENTS.SPECIAL_CHARS && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    return { isValid: errors.length === 0, errors };
  }

  static validateUsername(username: string): boolean {
    if (!username || typeof username !== 'string') return false;
    
    // Username should be 3-30 characters, alphanumeric and underscores only
    const usernameRegex = /^[a-zA-Z0-9_]{3,30}$/;
    return usernameRegex.test(username);
  }

  static validatePhoneNumber(phone: string): boolean {
    if (!phone || typeof phone !== 'string') return false;
    
    // Basic phone number validation (international format)
    const phoneRegex = /^\+?[\d\s\-\(\)]{7,20}$/;
    return phoneRegex.test(phone);
  }

  static validateFileUpload(file: File): { isValid: boolean; error?: string } {
    if (!file) {
      return { isValid: false, error: 'No file provided' };
    }

    if (!SECURITY_CONFIG.ALLOWED_FILE_TYPES.includes(file.type)) {
      return { isValid: false, error: 'Invalid file type. Only JPEG, PNG, and WebP images are allowed.' };
    }

    if (file.size > SECURITY_CONFIG.MAX_FILE_SIZE) {
      return { isValid: false, error: `File size too large. Maximum size is ${SECURITY_CONFIG.MAX_FILE_SIZE / (1024 * 1024)}MB.` };
    }

    return { isValid: true };
  }

  static sanitizeObject(obj: any): any {
    if (Array.isArray(obj)) {
      return obj.map(item => this.sanitizeObject(item));
    }
    
    if (obj && typeof obj === 'object') {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(obj)) {
        if (typeof value === 'string') {
          sanitized[key] = this.sanitizeString(value);
        } else {
          sanitized[key] = this.sanitizeObject(value);
        }
      }
      return sanitized;
    }
    
    return obj;
  }
}

// Edge stub for AuthSecurity
export class AuthSecurity {
  static generateSecureToken(): string { throw new Error('Not available in Edge Runtime'); }
  static hashPassword(_: string): string { throw new Error('Not available in Edge Runtime'); }
  static verifyPassword(_: string, __: string): boolean { throw new Error('Not available in Edge Runtime'); }
  static generateSessionId(): string { throw new Error('Not available in Edge Runtime'); }
}

// Edge stub for CSRFProtection
export class CSRFProtection {
  static generateToken(): string { throw new Error('Not available in Edge Runtime'); }
  static validateToken(_: string, __: string): boolean { throw new Error('Not available in Edge Runtime'); }
  static addCSRFTokenToResponse(): never { throw new Error('Not available in Edge Runtime'); }
}

// Stubs for missing exports
export const rateLimit = () => { throw new Error('Not available in Edge Runtime'); };
export const setSecurityHeaders = () => { throw new Error('Not available in Edge Runtime'); };
export const validateCSRFToken = () => { throw new Error('Not available in Edge Runtime'); };
export const validateInput = () => { throw new Error('Not available in Edge Runtime'); };
export const sanitizeInput = () => { throw new Error('Not available in Edge Runtime'); };
export const generateCSRFToken = () => { throw new Error('Not available in Edge Runtime'); };
export const validateAuthToken = () => { throw new Error('Not available in Edge Runtime'); };

// Rate Limiting
export class RateLimiter {
  private static attempts = new Map<string, { count: number; resetTime: number }>();

  static checkRateLimit(identifier: string): { allowed: boolean; remainingAttempts: number; resetTime: number } {
    const now = Date.now();
    const userAttempts = this.attempts.get(identifier);

    if (!userAttempts || now > userAttempts.resetTime) {
      // Reset or initialize
      this.attempts.set(identifier, {
        count: 1,
        resetTime: now + SECURITY_CONFIG.RATE_LIMIT_WINDOW,
      });
      return {
        allowed: true,
        remainingAttempts: SECURITY_CONFIG.RATE_LIMIT_MAX_REQUESTS - 1,
        resetTime: now + SECURITY_CONFIG.RATE_LIMIT_WINDOW,
      };
    }

    if (userAttempts.count >= SECURITY_CONFIG.RATE_LIMIT_MAX_REQUESTS) {
      return {
        allowed: false,
        remainingAttempts: 0,
        resetTime: userAttempts.resetTime,
      };
    }

    userAttempts.count++;
    return {
      allowed: true,
      remainingAttempts: SECURITY_CONFIG.RATE_LIMIT_MAX_REQUESTS - userAttempts.count,
      resetTime: userAttempts.resetTime,
    };
  }

  static clearAttempts(identifier: string): void {
    this.attempts.delete(identifier);
  }
}

// Security Headers
export const getSecurityHeaders = () => ({
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
  'Content-Security-Policy': [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://connect.facebook.net https://www.googletagmanager.com",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com",
    "img-src 'self' data: https: blob:",
    "connect-src 'self' https://fundedhorizon-back-e4285707ccdf.herokuapp.com https://www.facebook.com",
    "frame-src 'self' https://www.facebook.com",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "frame-ancestors 'none'",
    "upgrade-insecure-requests"
  ].join('; '),
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=(), payment=()',
  'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
  'Pragma': 'no-cache',
  'Expires': '0',
});

// Request Validation
export const validateRequest = (request: NextRequest) => {
  const userAgent = request.headers.get('user-agent');
  const origin = request.headers.get('origin');
  
  // Block suspicious user agents
  const suspiciousUserAgents = [
    'sqlmap', 'nikto', 'nmap', 'w3af', 'burp', 'zap', 'acunetix', 'nessus'
  ];
  
  if (userAgent && suspiciousUserAgents.some(agent => userAgent.toLowerCase().includes(agent))) {
    return { valid: false, reason: 'Suspicious user agent detected' };
  }

  // Validate origin for CORS (more lenient in production)
  if (origin && process.env.NODE_ENV === 'production') {
    // Allow common production origins
    const allowedOrigins = [
      'https://fundedhorizon.com',
      'https://www.fundedhorizon.com',
      'https://fhhou-866cad755156.herokuapp.com',
      'https://funded-horizon.vercel.app',
      'https://funded-horizon.netlify.app'
    ];
    
    // Also allow if ALLOWED_ORIGINS env var is set
    const envAllowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || [];
    const allAllowedOrigins = [...allowedOrigins, ...envAllowedOrigins];
    
    // Check if origin is in allowed list or if it's a subdomain of allowed domains
    const isAllowed = allAllowedOrigins.some(allowed => {
      return origin === allowed || origin.endsWith('.' + allowed.replace('https://', ''));
    });
    
    if (!isAllowed) {
      // Log but don't block in production unless it's clearly malicious
      console.warn('Origin validation failed:', origin);
      // Only block if it's clearly not a legitimate request
      if (!origin.includes('fundedhorizon') && !origin.includes('localhost')) {
      return { valid: false, reason: 'Invalid origin' };
      }
    }
  }

  return { valid: true };
};

// SQL Injection Prevention
export const sanitizeSQLInput = (input: string): string => {
  if (typeof input !== 'string') return '';
  
  // Remove SQL injection patterns - Enhanced version
  const sqlPatterns = [
    // Basic SQL keywords
    /(\b(union|select|insert|update|delete|drop|create|alter|exec|execute|script|javascript|vbscript|onload|onerror|onclick)\b)/gi,
    // SQL operators and conditions
    /(\b(and|or|not|like|between|in|exists|all|any|some)\b)/gi,
    // SQL functions
    /(\b(count|sum|avg|max|min|concat|substring|char|ascii|hex|bin|oct|length|trim|upper|lower)\b)/gi,
    // SQL comments and delimiters
    /(['";\\])/g,
    /(\-\-|\/\*|\*\/)/g,
    // SQL injection patterns
    /(\b(and|or)\b\s+\d+\s*=\s*\d+)/gi,
    /(\b(and|or)\b\s+['"]\w+['"]\s*=\s*['"]\w+['"])/gi,
    // Union-based attacks
    /(\bunion\b.*\bselect\b)/gi,
    /(\bselect\b.*\bunion\b)/gi,
    // Stacked queries
    /(\bselect\b.*\binsert\b)/gi,
    /(\bselect\b.*\bupdate\b)/gi,
    /(\bselect\b.*\bdelete\b)/gi,
    // Time-based attacks
    /(\bsleep\b|\bwaitfor\b|\bdelay\b)/gi,
    // Boolean-based attacks
    /(\btrue\b|\bfalse\b|\bnull\b)/gi,
    // Information schema attacks
    /(\binformation_schema\b|\bsys\b|\bsystem\b)/gi,
    // File operations
    /(\bload_file\b|\binto\s+outfile\b|\binto\s+dumpfile\b)/gi,
    // Stored procedures
    /(\bsp_\b|\bxp_\b|\bmsys\b)/gi,
    // Hex and binary encoding
    /(0x[0-9a-fA-F]+)/g,
    // URL encoding of SQL
    /(%27|%22|%3B|%2D%2D|%2F%2A|%2A%2F)/gi,
    // Unicode encoding
    /(\u0027|\u0022|\u003B|\u002D\u002D|\u002F\u002A|\u002A\u002F)/gi,
    // Double encoding
    /(%25%27|%25%22|%25%3B|%25%2D%25%2D)/gi
  ];
  
  let sanitized = input;
  sqlPatterns.forEach(pattern => {
    sanitized = sanitized.replace(pattern, '');
  });
  
  // Additional sanitization
  sanitized = sanitized
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/[()]/g, '') // Remove parentheses
    .replace(/[{}]/g, '') // Remove braces
    .replace(/[\[\]]/g, '') // Remove brackets
    .replace(/[|&]/g, '') // Remove pipes and ampersands
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim();
  
  return sanitized;
};

// Enhanced SQL injection detection
export const detectSQLInjection = (input: string): boolean => {
  if (typeof input !== 'string') return false;
  
  const sqlInjectionPatterns = [
    // Basic SQL keywords in suspicious context
    /\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\b/i,
    // SQL operators with suspicious patterns
    /\b(and|or)\b\s+\d+\s*=\s*\d+/i,
    /\b(and|or)\b\s+['"]\w+['"]\s*=\s*['"]\w+['"]/i,
    // Union-based attacks
    /\bunion\b.*\bselect\b/i,
    /\bselect\b.*\bunion\b/i,
    // Stacked queries
    /\bselect\b.*\binsert\b/i,
    /\bselect\b.*\bupdate\b/i,
    /\bselect\b.*\bdelete\b/i,
    // Time-based attacks
    /\b(sleep|waitfor|delay)\b/i,
    // Information schema
    /\binformation_schema\b/i,
    // File operations
    /\b(load_file|into\s+outfile|into\s+dumpfile)\b/i,
    // Stored procedures
    /\b(sp_|xp_|msys)\b/i,
    // Comments
    /(\-\-|\/\*|\*\/)/i,
    // Hex values
    /0x[0-9a-fA-F]+/i,
    // URL encoded SQL
    /(%27|%22|%3B|%2D%2D|%2F%2A|%2A%2F)/i,
    // Unicode encoded SQL
    /(\u0027|\u0022|\u003B|\u002D\u002D|\u002F\u002A|\u002A\u002F)/i
  ];
  
  return sqlInjectionPatterns.some(pattern => pattern.test(input));
};

// Validate and sanitize URL parameters
export const sanitizeURLParams = (params: Record<string, string>): Record<string, string> => {
  const sanitized: Record<string, string> = {};
  
  for (const [key, value] of Object.entries(params)) {
    if (detectSQLInjection(value)) {
      logSecurityEvent('SQL injection attempt detected in URL params', {
        key,
        value: value.substring(0, 100), // Log first 100 chars
        originalValue: value
      }, 'high');
      sanitized[key] = '';
    } else {
      sanitized[key] = sanitizeSQLInput(value);
    }
  }
  
  return sanitized;
};

// Validate and sanitize request body
export const sanitizeRequestBody = (body: any): any => {
  if (typeof body === 'string') {
    if (detectSQLInjection(body)) {
      logSecurityEvent('SQL injection attempt detected in request body', {
        body: body.substring(0, 100)
      }, 'high');
      return '';
    }
    return sanitizeSQLInput(body);
  }
  
  if (Array.isArray(body)) {
    return body.map(item => sanitizeRequestBody(item));
  }
  
  if (body && typeof body === 'object') {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(body)) {
      sanitized[key] = sanitizeRequestBody(value);
    }
    return sanitized;
  }
  
  return body;
};

// Logging for Security Events
export const logSecurityEvent = (event: string, details: any, severity: 'low' | 'medium' | 'high' | 'critical' = 'medium') => {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    event,
    severity,
    details,
    userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'server',
    ip: 'client-ip', // Would be set by middleware
  };
  
  console.error(`[SECURITY ${severity.toUpperCase()}] ${timestamp}: ${event}`, logEntry);
  
  // In production, send to security monitoring service
  if (process.env.NODE_ENV === 'production') {
    // TODO: Implement security monitoring service integration
  }
}; 