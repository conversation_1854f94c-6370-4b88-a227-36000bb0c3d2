import { useState, useEffect } from "react"
import { <PERSON>, <PERSON>Chart, Eye, EyeOff, Award } from "lucide-react"
import { OrderDetails, OrderStatus, OrderType, RejectReasons, FailReasons } from "@/types/order"
import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

interface EditOrderModalProps {
  order: OrderDetails
  onSave: (order: OrderDetails) => Promise<void>
  onFail: (order: OrderDetails, reason: FailReasons) => Promise<void>
  onReject: (order: OrderDetails, reason: RejectReasons) => Promise<void>
}

export function EditOrderModal({ order, onSave, onFail, onReject }: EditOrderModalProps) {
  const [editedOrder, setEditedOrder] = useState<OrderDetails>(order)
  const [open, setOpen] = useState(false)
  const [showPassword, setShowPassword] = useState(false)

  const handleSaveChanges = () => {
    // Just update the local state through onSave callback
    onSave(editedOrder)
    setOpen(false)
  }

  // Reset edited order when modal opens with new order
  useEffect(() => {
    setEditedOrder(order)
  }, [order])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setEditedOrder({ ...editedOrder, [name]: value });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button 
          variant="outline"
          size="sm"
          className="w-full md:w-auto bg-gradient-to-r from-purple-900 to-purple-700 hover:from-purple-950 hover:to-purple-800 text-white border-purple-700 shadow-xl transition duration-300 rounded-xl"
        >
          Edit Order
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-[90vw] md:max-w-2xl bg-gradient-to-b from-gray-900 to-gray-950 border border-purple-800/30 shadow-2xl rounded-xl p-3 md:p-4 overflow-y-auto max-h-[85vh]">
        <DialogHeader className="relative">
          <div className="absolute top-0 right-0 bg-purple-900/30 px-2 py-0.5 rounded-lg text-[10px] text-purple-300 font-mono backdrop-blur-sm border border-purple-800/30">
            ID: {editedOrder.id}
          </div>
          <DialogTitle className="text-base md:text-lg font-bold text-white flex items-center gap-2">
            <div className="p-1.5 bg-purple-900/50 rounded-lg shadow-lg backdrop-blur-sm">
              <Crown className="w-4 h-4 text-purple-300" />
            </div>
            Order Management
          </DialogTitle>
          <DialogDescription className="text-gray-400 text-xs mt-0.5">
            Manage and modify order details securely.
          </DialogDescription>
        </DialogHeader>

        <div className="mt-3 space-y-3">
          {/* Main Section */}
          <div className="space-y-2 bg-gray-800/30 p-3 rounded-xl border border-purple-800/30 shadow-lg backdrop-blur-sm">
            <h3 className="text-xs font-bold text-white flex items-center gap-1.5">
              <LineChart className="w-3.5 h-3.5 text-purple-400" />
              Platform Details
            </h3>
            
            <div className="space-y-2">
              {/* Profit Target */}
              <div>
                <Label className="text-gray-300 text-xs">Profit Target (%)</Label>
                <Input 
                  name="profitTarget"
                  value={editedOrder?.profitTarget?.toString() || ''} 
                  onChange={handleChange}
                  className="mt-0.5 bg-gray-700/30 border-purple-700/50 text-white rounded-lg text-xs h-7 focus:ring-purple-500 focus:border-purple-500" 
                />
              </div>

              {/* Platform Credentials */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                <div>
                  <Label className="text-gray-300 text-xs">Login</Label>
                  <Input 
                    name="platformLogin"
                    value={editedOrder?.platformLogin || ''} 
                    onChange={handleChange}
                    className="mt-0.5 bg-gray-700/30 border-purple-700/50 text-white rounded-lg text-xs h-7 focus:ring-purple-500 focus:border-purple-500" 
                  />
                </div>
                <div>
                  <Label className="text-gray-300 text-xs">Password</Label>
                  <div className="relative">
                    <Input 
                      name="platformPassword"
                      value={editedOrder?.platformPassword || ''} 
                      onChange={handleChange}
                      type={showPassword ? "text" : "password"}
                      className="mt-0.5 bg-gray-700/30 border-purple-700/50 text-white rounded-lg text-xs h-7 focus:ring-purple-500 focus:border-purple-500 pr-8" 
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
                    >
                      {showPassword ? (
                        <EyeOff className="h-3.5 w-3.5" />
                      ) : (
                        <Eye className="h-3.5 w-3.5" />
                      )}
                    </button>
                  </div>
                </div>
                <div>
                  <Label className="text-gray-300 text-xs">Server</Label>
                  <Input 
                    name="server"
                    value={editedOrder?.server || ''} 
                    onChange={handleChange}
                    className="mt-0.5 bg-gray-700/30 border-purple-700/50 text-white rounded-lg text-xs h-7 focus:ring-purple-500 focus:border-purple-500" 
                  />
                </div>
                <div>
                  <Label className="text-gray-300 text-xs">Session ID</Label>
                  <Input 
                    name="sessionId"
                    value={editedOrder?.sessionId || ''} 
                    onChange={handleChange}
                    className="mt-0.5 bg-gray-700/30 border-purple-700/50 text-white rounded-lg text-xs h-7 focus:ring-purple-500 focus:border-purple-500" 
                  />
                </div>
                <div>
                  <Label className="text-gray-300 text-xs">Terminal ID</Label>
                  <Input 
                    name="terminalId"
                    value={editedOrder?.terminalId || ''} 
                    onChange={handleChange}
                    className="mt-0.5 bg-gray-700/30 border-purple-700/50 text-white rounded-lg text-xs h-7 focus:ring-purple-500 focus:border-purple-500" 
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Order Status Section */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            <div>
              <Label className="text-gray-300 text-xs">Order Status</Label>
              <select 
                value={editedOrder.status} 
                onChange={(e) => {
                  const newStatus = e.target.value as OrderStatus;
                  let newAccountType = editedOrder.accountType;
                  
                  // Update account type based on status
                  if (newStatus === OrderStatus.STAGE_TWO) {
                    newAccountType = "Stage Two";
                  } else if (newStatus === OrderStatus.LIVE) {
                    newAccountType = "Live";
                  }
                  
                  setEditedOrder(prev => ({ 
                    ...prev, 
                    status: newStatus,
                    accountType: newAccountType
                  }));
                }}
                className="mt-0.5 w-full bg-gray-700/30 border-purple-700/50 text-white p-1.5 rounded-lg text-xs h-7 focus:ring-purple-500 focus:border-purple-500"
              >
                {Object.values(OrderStatus).map(status => (
                  <option key={status} value={status}>{status}</option>
                ))}
              </select>
            </div>
            <div>
              <Label className="text-gray-300 text-xs">Order Type</Label>
              <select 
                value={editedOrder.type} 
                onChange={(e) => setEditedOrder(prev => ({ ...prev, type: e.target.value as OrderType }))}
                className="mt-0.5 w-full bg-gray-700/30 border-purple-700/50 text-white p-1.5 rounded-lg text-xs h-7 focus:ring-purple-500 focus:border-purple-500"
              >
                {Object.values(OrderType).map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>
          </div>

          {/* Account Type Section */}
          <div className="space-y-2 bg-gray-800/30 p-3 rounded-xl border border-purple-800/30 shadow-lg backdrop-blur-sm mt-3">
            <h3 className="text-xs font-bold text-white flex items-center gap-1.5">
              <Award className="w-3.5 h-3.5 text-purple-400" />
              Account Status
            </h3>
            
            <div className="space-y-2">
              <div>
                <Label className="text-gray-300 text-xs">Account Type</Label>
                <select 
                  value={editedOrder.accountType || ''} 
                  onChange={(e) => {
                    const newAccountType = e.target.value;
                    let newStatus = editedOrder.status;
                    
                    // Update status based on account type
                    if (newAccountType === "Stage Two") {
                      newStatus = OrderStatus.STAGE_TWO;
                    } else if (newAccountType === "Live") {
                      newStatus = OrderStatus.LIVE;
                    } else {
                      newStatus = OrderStatus.PENDING;
                    }
                    
                    setEditedOrder(prev => ({ 
                      ...prev, 
                      accountType: newAccountType,
                      status: newStatus
                    }));
                  }}
                  className="mt-0.5 w-full bg-gray-700/30 border-purple-700/50 text-white p-1.5 rounded-lg text-xs h-7 focus:ring-purple-500 focus:border-purple-500"
                >
                  <option value="Challenge Phase-1">Challenge Phase-1</option>
                  <option value="Challenge Phase-2">Challenge Phase-2</option>
                  <option value="Stage Two">Stage Two</option>
                  <option value="Live">Live</option>
                </select>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label className="text-xs text-gray-400">Transaction ID</Label>
            <div className="flex flex-col space-y-1">
              {order.txid?.split(/(.{8})/).filter(Boolean).map((part, index) => (
                <span key={index} className="font-mono text-[10px] tracking-wider bg-gray-800/50 px-2 py-1 rounded">
                  {part}
                </span>
              ))}
            </div>
          </div>
        </div>

        <DialogFooter className="mt-3">
          <Button 
            type="submit" 
            onClick={handleSaveChanges}
            className="w-full sm:w-auto bg-gradient-to-r from-purple-900 to-purple-700 hover:from-purple-950 hover:to-purple-800 text-white text-xs rounded-lg py-1 px-4 shadow-lg transition duration-300"
          >
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
