import { NextRequest, NextResponse } from 'next/server';
import { getSecurityMetrics, analyzeRequest } from '@/app/lib/security-monitoring';
import { validateAuthToken } from '@/lib/node-security';
import { setSecurityHeaders } from '@/lib/security';

export async function GET(request: NextRequest) {
  try {
    // Analyze request for security threats
    const threatAnalysis = analyzeRequest(request);
    if (threatAnalysis.isThreat) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403, headers: setSecurityHeaders() }
      );
    }

    // Validate authentication token (admin only)
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401, headers: setSecurityHeaders() }
      );
    }

    const token = authHeader.substring(7);
    const tokenValidation = validateAuthToken(token);
    if (!tokenValidation.isValid || !tokenValidation.isAdmin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403, headers: setSecurityHeaders() }
      );
    }

    // Get security metrics
    const metrics = getSecurityMetrics();

    // Add additional security context
    const enhancedMetrics = {
      ...metrics,
      timestamp: new Date().toISOString(),
      requestAnalysis: {
        ip: request.ip || request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent'),
        threatLevel: threatAnalysis.riskLevel,
        threats: threatAnalysis.threats
      }
    };

    return NextResponse.json(enhancedMetrics, {
      status: 200,
      headers: setSecurityHeaders()
    });
  } catch (error) {
    console.error('Security metrics API error:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500, headers: setSecurityHeaders() }
    );
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      ...setSecurityHeaders(),
      'Access-Control-Allow-Origin': process.env.NEXT_PUBLIC_FRONTEND_URL || '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400'
    }
  });
} 