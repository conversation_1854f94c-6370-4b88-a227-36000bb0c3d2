import { useState } from "react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "lucide-react"
import { FailReasons, OrderDetails } from "@/types/order"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"

interface FailOrderModalProps {
  orderId: string
  onFail: (order: OrderDetails, reason: FailReasons) => Promise<void>
}

export function FailOrderModal({ orderId, onFail }: FailOrderModalProps) {
  const [open, setOpen] = useState(false)
  const [selectedReason, setSelectedReason] = useState<FailReasons | null>(null)
  const [failureDate, setFailureDate] = useState<string>(
    new Date().toISOString().split('T')[0]
  )
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleConfirm = async () => {
    if (!selectedReason) return
    
    setIsSubmitting(true)
    try {
      // Create a minimal order object with just the ID
      const orderObj = { id: parseInt(orderId), order_id: orderId } as OrderDetails
      await onFail(orderObj, selectedReason)
      setOpen(false)
      setSelectedReason(null)
    } catch (error) {
      console.error("Error failing order:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="bg-red-500 text-white hover:bg-red-600 rounded-lg text-xs"
        >
          Fail
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-xl bg-gray-900 border border-red-800/50 shadow-2xl rounded-xl">
        <DialogHeader>
          <DialogTitle className="text-lg font-bold text-white flex items-center gap-2">
            <div className="p-1.5 bg-red-900/50 rounded-lg">
              <AlertTriangle className="w-5 h-5 text-red-300" />
            </div>
            Fail Order
          </DialogTitle>
          <DialogDescription className="text-gray-400 text-sm mt-2">
            Please select a reason for failing this order.
          </DialogDescription>
        </DialogHeader>

        <div className="mt-6 space-y-4">
          <div className="mb-4">
            <label className="block text-gray-300 text-sm font-bold mb-2" htmlFor="failure-date">
              Failure Date
            </label>
            <input
              type="date"
              id="failure-date"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-white bg-gray-800 border-gray-600 leading-tight focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
              value={failureDate}
              onChange={(e) => setFailureDate(e.target.value)}
            />
          </div>
          <RadioGroup
            value={selectedReason}
            onValueChange={(value) => setSelectedReason(value as FailReasons)}
            className="grid grid-cols-1 gap-2"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value={FailReasons.DAILY_DRAWDOWN_5} id="daily-drawdown-5" />
              <Label htmlFor="daily-drawdown-5" className="text-xs text-gray-400">
                {FailReasons.DAILY_DRAWDOWN_5}
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value={FailReasons.OVERALL_DRAWDOWN} id="overall-drawdown" />
              <Label htmlFor="overall-drawdown" className="text-xs text-gray-400">
                {FailReasons.OVERALL_DRAWDOWN}
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value={FailReasons.PHASE1_TRADING_DAYS} id="phase1-trading-days" />
              <Label htmlFor="phase1-trading-days" className="text-xs text-gray-400">
                {FailReasons.PHASE1_TRADING_DAYS}
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value={FailReasons.DAILY_DRAWDOWN_4} id="daily-drawdown-4" />
              <Label htmlFor="daily-drawdown-4" className="text-xs text-gray-400">
                {FailReasons.DAILY_DRAWDOWN_4}
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value={FailReasons.PHASE2_TRADING_DAYS} id="phase2-trading-days" />
              <Label htmlFor="phase2-trading-days" className="text-xs text-gray-400">
                {FailReasons.PHASE2_TRADING_DAYS}
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value={FailReasons.DAILY_LOSS} id="daily-loss" />
              <Label htmlFor="daily-loss" className="text-xs text-gray-400">
                {FailReasons.DAILY_LOSS}
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value={FailReasons.TOTAL_LOSS} id="total-loss" />
              <Label htmlFor="total-loss" className="text-xs text-gray-400">
                {FailReasons.TOTAL_LOSS}
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value={FailReasons.OTHER} id="other" />
              <Label htmlFor="other" className="text-xs text-gray-400">
                {FailReasons.OTHER}
              </Label>
            </div>
          </RadioGroup>
        </div>

        <DialogFooter className="mt-6 space-x-2">
          <Button
            variant="outline"
            onClick={() => setOpen(false)}
            className="bg-transparent text-white border-red-500/20 hover:bg-red-500/10"
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={!selectedReason || isSubmitting}
            className={`${
              selectedReason && !isSubmitting
                ? 'bg-red-500 hover:bg-red-600' 
                : 'bg-red-500/50 cursor-not-allowed'
            } text-white transition-colors duration-200`}
          >
            {isSubmitting ? "Processing..." : "Confirm Failure"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 