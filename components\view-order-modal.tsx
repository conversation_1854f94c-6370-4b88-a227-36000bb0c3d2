import { motion, AnimatePresence } from "framer-motion"
import { <PERSON><PERSON>ard, User, Wallet, Shield, Alert<PERSON><PERSON>gle, <PERSON><PERSON>, Check } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { OrderDetails, OrderStatus } from "@/types/order"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { useToast } from "@/components/ui/use-toast"
import { useState } from "react"

// Update the OrderDetails interface to include additional fields
interface ExtendedOrderDetails extends OrderDetails {
  order_id?: string
  sessionId?: string
  terminalId?: string
  reason?: string
  complete_order_id?: string
  fail_order_id?: string
  reject_order_id?: string
}

interface ViewOrderModalProps {
  order: ExtendedOrderDetails
  onView: (order: ExtendedOrderDetails) => void
  status?: OrderStatus | string
}

export function ViewOrderModal({ order, onView, status }: ViewOrderModalProps) {
  const { toast } = useToast()
  const [copied, setCopied] = useState(false)
  
  const handleCopyTxid = () => {
    if (order.txid) {
      navigator.clipboard.writeText(order.txid)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
      toast({
        title: "Copied!",
        description: "Transaction ID copied to clipboard",
        className: "bg-green-900/50 text-white border-green-500/50 backdrop-blur-sm",
      })
    }
  }

  // Use the provided status or fall back to the order's status
  const displayStatus = status || order.status
  
  // Helper function to check status
  const isRejected = displayStatus === "Rejected"
  const isCancelled = displayStatus === OrderStatus.CANCELLED
  const isCompleted = displayStatus === OrderStatus.COMPLETED
  const isPending = displayStatus === OrderStatus.PENDING

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          onClick={() => onView(order)}
          className="w-full md:w-auto text-xs"
        >
          View
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-[95vw] md:max-w-3xl max-h-[85vh] bg-gradient-to-b from-gray-900 to-gray-950 border border-orange-800/50 shadow-2xl rounded-xl overflow-hidden">
        <DialogHeader className="sticky top-0 z-10 p-6 border-b border-orange-800/20 bg-black/20 backdrop-blur-sm">
          <DialogTitle className="text-lg font-bold text-white flex items-center gap-2">
            <div className="p-1.5 bg-orange-900/50 rounded-lg shadow-xl ring-1 ring-orange-500/20">
              <CreditCard className="w-5 h-5 text-orange-300" />
            </div>
            Order Details
          </DialogTitle>
          <DialogDescription className="text-gray-400 text-sm mt-2">
            Complete information about the order and customer.
          </DialogDescription>
        </DialogHeader>

        <div className="overflow-y-auto max-h-[calc(85vh-8rem)] p-6 space-y-6 custom-scrollbar">
          {/* Customer Information */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-4 transform hover:scale-[1.01] transition-transform duration-200"
          >
            <h3 className="text-sm font-semibold text-white flex items-center gap-2">
              <div className="p-1 bg-blue-500/10 rounded-md ring-1 ring-blue-500/20">
                <User className="h-4 w-4 text-blue-400" />
              </div>
              Customer Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 bg-black/40 p-4 rounded-xl border border-blue-500/20 shadow-xl">
              <div className="space-y-1">
                <p className="text-xs text-gray-400">Full Name</p>
                <p className="text-sm font-medium text-white">{order.user.name}</p>
              </div>
              <div className="space-y-1">
                <p className="text-xs text-gray-400">Email Address</p>
                <p className="text-sm font-medium text-white">{order.user.email}</p>
              </div>
              <div className="space-y-1">
                <p className="text-xs text-gray-400">Password</p>
                <p className="text-sm font-medium text-white">
                  {(order.user as ExtendedUser).password || (order.user as ExtendedUser).hashed_password || 'N/A'}
                </p>
              </div>
            </div>
          </motion.div>

          {/* Order Information */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
            className="space-y-4 transform hover:scale-[1.01] transition-transform duration-200"
          >
            <h3 className="text-sm font-semibold text-white flex items-center gap-2">
              <div className="p-1 bg-orange-500/10 rounded-md ring-1 ring-orange-500/20">
                <CreditCard className="h-4 w-4 text-orange-400" />
              </div>
              Order Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 bg-black/40 p-4 rounded-xl border border-orange-500/20 shadow-xl">
              <div className="space-y-1">
                <p className="text-xs text-gray-400">Order ID</p>
                <p className="text-sm font-medium text-white">
                  {order.order_id || order.id || 
                   order.complete_order_id || 
                   order.fail_order_id || 
                   order.reject_order_id || 'N/A'}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-xs text-gray-400">Status</p>
                <Badge 
                  variant="outline" 
                  className={
                    isCompleted
                      ? "bg-green-500/10 text-green-400 border-green-500/20"
                      : isPending
                      ? "bg-yellow-500/10 text-yellow-400 border-yellow-500/20"
                      : "bg-red-500/10 text-red-400 border-red-500/20"
                  }
                >
                  {displayStatus}
                </Badge>
              </div>
              <div className="space-y-1">
                <p className="text-xs text-gray-400">Account Type</p>
                <p className="text-sm font-medium text-white">{order.accountType}</p>
              </div>
              <div className="space-y-1">
                <p className="text-xs text-gray-400">Platform</p>
                <p className="text-sm font-medium text-white">{order.platformType}</p>
              </div>
              <div className="space-y-1">
                <p className="text-xs text-gray-400">Created At</p>
                <p className="text-sm font-medium text-white">{order.createdAt}</p>
              </div>
              {order.profitTarget && (
                <div className="space-y-1">
                  <p className="text-xs text-gray-400">Profit Target</p>
                  <p className="text-sm font-medium text-white">${order.profitTarget}</p>
                </div>
              )}
            </div>
          </motion.div>

          {/* Payment Information */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
            className="space-y-4 transform hover:scale-[1.01] transition-transform duration-200"
          >
            <h3 className="text-sm font-semibold text-white flex items-center gap-2">
              <div className="p-1 bg-green-500/10 rounded-md ring-1 ring-green-500/20">
                <Wallet className="h-4 w-4 text-green-400" />
              </div>
              Payment Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 bg-black/40 p-4 rounded-xl border border-green-500/20 shadow-xl">
              <div className="space-y-1">
                <p className="text-xs text-gray-400">Payment Method</p>
                <p className="text-sm font-medium text-white">{order.paymentMethod || 'N/A'}</p>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="text-xs text-gray-400">Transaction ID</Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCopyTxid}
                    className="h-6 px-2 text-xs hover:bg-gray-800/50"
                  >
                    <AnimatePresence mode="wait">
                      {copied ? (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          exit={{ scale: 0 }}
                          className="flex items-center gap-1 text-green-400"
                        >
                          <Check className="h-3 w-3" />
                          <span>Copied</span>
                        </motion.div>
                      ) : (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          exit={{ scale: 0 }}
                          className="flex items-center gap-1"
                        >
                          <Copy className="h-3 w-3" />
                          <span>Copy</span>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </Button>
                </div>
                <div className="flex flex-col space-y-1">
                  {order.txid?.split(/(.{30})/).filter(Boolean).map((part, index) => (
                    <span key={index} className="font-mono text-[10px] tracking-wider bg-gray-800/50 px-2 py-1 rounded">
                      {part}
                    </span>
                  ))}
                </div>
              </div>
              <div className="space-y-1">
                <p className="text-xs text-gray-400">Current Balance</p>
                <p className="text-sm font-medium text-white">${order.currentBalance?.toLocaleString() || '0'}</p>
              </div>
            </div>
          </motion.div>

          {/* Platform Credentials */}
          {(order.platformLogin || order.sessionId || order.terminalId) && (
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.3 }}
              className="space-y-4 transform hover:scale-[1.01] transition-transform duration-200"
            >
              <h3 className="text-sm font-semibold text-white flex items-center gap-2">
                <div className="p-1 bg-purple-500/10 rounded-md ring-1 ring-purple-500/20">
                  <Shield className="h-4 w-4 text-purple-400" />
                </div>
                Platform Credentials
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 bg-black/40 p-4 rounded-xl border border-purple-500/20 shadow-xl">
                <div className="space-y-1">
                  <p className="text-xs text-gray-400">Login</p>
                  <p className="text-sm font-medium text-white">{order.platformLogin}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-xs text-gray-400">Password</p>
                  <p className="text-sm font-medium text-white">{order.platformPassword || 'N/A'}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-xs text-gray-400">Server</p>
                  <p className="text-sm font-medium text-white">{order.server || 'N/A'}</p>
                </div>
                {order.sessionId && (
                  <div className="space-y-1">
                    <p className="text-xs text-gray-400">Session ID</p>
                    <p className="text-sm font-medium text-white">{order.sessionId}</p>
                  </div>
                )}
                {order.terminalId && (
                  <div className="space-y-1">
                    <p className="text-xs text-gray-400">Terminal ID</p>
                    <p className="text-sm font-medium text-white">{order.terminalId}</p>
                  </div>
                )}
              </div>
            </motion.div>
          )}

          {/* Failure/Rejection Reason */}
          {(isCancelled || isRejected) && order.reason && (
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.4 }}
              className="space-y-4 transform hover:scale-[1.01] transition-transform duration-200"
            >
              <h3 className="text-sm font-semibold text-white flex items-center gap-2">
                <div className="p-1 bg-red-500/10 rounded-md ring-1 ring-red-500/20">
                  <AlertTriangle className="h-4 w-4 text-red-400" />
                </div>
                {isCancelled ? 'Failure Reason' : 'Rejection Reason'}
              </h3>
              <div className="bg-black/40 p-4 rounded-xl border border-red-500/20 shadow-xl">
                <p className="text-sm font-medium text-white">{order.reason}</p>
              </div>
            </motion.div>
          )}
        </div>

        <style jsx global>{`
          .custom-scrollbar::-webkit-scrollbar {
            width: 8px;
          }
          .custom-scrollbar::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 4px;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb {
            background: rgba(251, 146, 60, 0.3);
            border-radius: 4px;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: rgba(251, 146, 60, 0.5);
          }
        `}</style>
      </DialogContent>
    </Dialog>
  )
}