"use client"

import { Inter } from "next/font/google"
import { Sidebar } from "@/components/sidebar"
import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import secureStorageAPI from "../lib/secureStorage"
import "./globals.css"

const inter = Inter({ subsets: ["latin"] })

// Helper function to get cookie value
const getCookie = (name: string): string | null => {
  if (typeof document === 'undefined') return null;
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
  return null;
};

// Helper function to check if token is expired (24 hours)
const isTokenExpired = (loginTime: string | null): boolean => {
  if (!loginTime) return true;
  const loginTimestamp = parseInt(loginTime);
  const currentTime = Date.now();
  const twentyFourHours = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
  return (currentTime - loginTimestamp) > twentyFourHours;
};

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const router = useRouter()
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null)
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
    
    // Check for authentication using the new utility
    if (!secureStorageAPI.isAuthenticated()) {
      // Clear any invalid tokens
      secureStorageAPI.secureLogout();
      
      setIsAuthenticated(false);
      router.push('/sigin');
      return;
    }
    
    setIsAuthenticated(true);
  }, [router]);

  // Show loading while checking authentication
  if (!isClient || isAuthenticated === null) {
    return (
      <html lang="en" className="dark">
        <body className={inter.className}>
          <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500"></div>
          </div>
        </body>
      </html>
    )
  }

  // Redirect if not authenticated
  if (!isAuthenticated) {
    return null
  }

  return (
    <html lang="en" className="dark">
      <body className={inter.className}>
        <div className="flex min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
          {/* Fixed Sidebar */}
          <Sidebar />
          
          {/* Main Content Area */}
          <div className="flex-1 md:pl-80"> {/* Make padding responsive */}
            <div className="relative min-h-screen">
              {/* Enhanced Background Effects */}
              <div className="absolute inset-0">
                {/* Grid Pattern */}
                <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,rgba(156,146,172,0.1)_1px,transparent_0)] bg-[length:20px_20px]" />
                
                {/* Gradient Overlays */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-purple-500/5" />
                <div className="absolute inset-0 bg-gradient-to-tl from-orange-500/3 via-transparent to-blue-500/3" />
                
                {/* Animated Background Elements */}
                <div className="absolute top-20 left-20 w-72 h-72 bg-blue-500/5 rounded-full blur-3xl animate-pulse"></div>
                <div className="absolute bottom-20 right-20 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-gradient-to-r from-blue-500/3 to-purple-500/3 rounded-full blur-3xl animate-pulse delay-500"></div>
              </div>
              
              {/* Content */}
              <div className="relative z-10 pt-16 md:pt-0"> {/* Add top padding for mobile menu */}
                {children}
              </div>
            </div>
          </div>
        </div>
      </body>
    </html>
  )
}
