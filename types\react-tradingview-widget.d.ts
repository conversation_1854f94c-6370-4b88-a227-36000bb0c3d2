declare module 'react-tradingview-widget' {
  interface TradingViewWidgetProps {
    symbol?: string;
    theme?: string;
    autosize?: boolean;
    style?: string;
    locale?: string;
    interval?: string;
    timezone?: string;
    studies?: string[];
    width?: number | string;
    height?: number | string;
  }

  export default function TradingViewWidget(props: TradingViewWidgetProps): JSX.Element;
}