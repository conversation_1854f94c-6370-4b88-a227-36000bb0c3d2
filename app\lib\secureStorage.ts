import { AuthSecurity } from '@/lib/security';

// Secure storage interface
interface SecureStorage {
  getItem(key: string): string | null;
  setItem(key: string, value: string): void;
  removeItem(key: string): void;
  clear(): void;
  setSensitiveItem(key: string, value: string): void;
  getSensitiveItem(key: string): string | null;
  hasItem(key: string): boolean;
  getAllKeys(): string[];
  secureLogout(): void;
  isAuthenticated(): boolean;
  getAuthInfo(): { token: string | null; email: string | null; loginTime: string | null };
  setAuthInfo(token: string, email: string): void;
}

// In-memory storage for sensitive data (cleared on page refresh)
class MemoryStorage implements SecureStorage {
  private storage = new Map<string, string>();

  getItem(key: string): string | null {
    return this.storage.get(key) || null;
  }

  setItem(key: string, value: string): void {
    this.storage.set(key, value);
  }

  removeItem(key: string): void {
    this.storage.delete(key);
  }

  clear(): void {
    this.storage.clear();
  }

  setSensitiveItem(key: string, value: string): void {
    this.storage.set(key, value);
  }

  getSensitiveItem(key: string): string | null {
    return this.storage.get(key) || null;
  }

  hasItem(key: string): boolean {
    return this.storage.has(key);
  }

  getAllKeys(): string[] {
    return Array.from(this.storage.keys());
  }

  secureLogout(): void {
    this.clear();
  }

  isAuthenticated(): boolean {
    const token = this.getItem('access_token');
    const loginTime = this.getItem('login_time');
    
    if (!token || !loginTime) return false;
    
    // Check if token is expired (24 hours)
    const loginTimestamp = parseInt(loginTime);
    const currentTime = Date.now();
    const twentyFourHours = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
    
    return (currentTime - loginTimestamp) <= twentyFourHours;
  }

  getAuthInfo(): { token: string | null; email: string | null; loginTime: string | null } {
    return {
      token: this.getItem('access_token'),
      email: this.getItem('user_email'),
      loginTime: this.getItem('login_time')
    };
  }

  setAuthInfo(token: string, email: string): void {
    const loginTime = Date.now().toString();
    
    // Store in memory storage
    this.setItem('access_token', token);
    this.setItem('user_email', email);
    this.setItem('login_time', loginTime);
    
    // Also store in cookies for middleware access
    if (typeof document !== 'undefined') {
      document.cookie = `access_token=${token}; path=/; max-age=86400; SameSite=Strict`;
      document.cookie = `user_email=${email}; path=/; max-age=86400; SameSite=Strict`;
      document.cookie = `login_time=${loginTime}; path=/; max-age=86400; SameSite=Strict`;
    }
  }
}

// Encrypted localStorage wrapper
class EncryptedStorage implements SecureStorage {
  private encryptionKey: string;

  constructor() {
    // Generate a unique encryption key for this session (browser-safe)
    if (typeof window !== 'undefined' && window.crypto && window.crypto.getRandomValues) {
      this.encryptionKey = Array.from(window.crypto.getRandomValues(new Uint32Array(4))).join('-');
    } else {
      this.encryptionKey = Math.random().toString(36).substring(2) + Date.now();
    }
  }

  private encrypt(text: string): string {
    try {
      // Simple XOR encryption (in production, use a proper encryption library)
      let encrypted = '';
      for (let i = 0; i < text.length; i++) {
        const charCode = text.charCodeAt(i) ^ this.encryptionKey.charCodeAt(i % this.encryptionKey.length);
        encrypted += String.fromCharCode(charCode);
      }
      return btoa(encrypted); // Base64 encode
    } catch (error) {
      console.error('Encryption failed:', error);
      return '';
    }
  }

  private decrypt(encryptedText: string): string {
    try {
      const decoded = atob(encryptedText); // Base64 decode
      let decrypted = '';
      for (let i = 0; i < decoded.length; i++) {
        const charCode = decoded.charCodeAt(i) ^ this.encryptionKey.charCodeAt(i % this.encryptionKey.length);
        decrypted += String.fromCharCode(charCode);
      }
      return decrypted;
    } catch (error) {
      console.error('Decryption failed:', error);
      return '';
    }
  }

  getItem(key: string): string | null {
    try {
      const encrypted = localStorage.getItem(key);
      if (!encrypted) return null;
      return this.decrypt(encrypted);
    } catch (error) {
      console.error('Failed to get encrypted item:', error);
      return null;
    }
  }

  setItem(key: string, value: string): void {
    try {
      const encrypted = this.encrypt(value);
      localStorage.setItem(key, encrypted);
    } catch (error) {
      console.error('Failed to set encrypted item:', error);
    }
  }

  removeItem(key: string): void {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('Failed to remove encrypted item:', error);
    }
  }

  clear(): void {
    try {
      localStorage.clear();
    } catch (error) {
      console.error('Failed to clear encrypted storage:', error);
    }
  }

  setSensitiveItem(key: string, value: string): void {
    this.setItem(key, value);
  }

  getSensitiveItem(key: string): string | null {
    return this.getItem(key);
  }

  hasItem(key: string): boolean {
    return this.getItem(key) !== null;
  }

  getAllKeys(): string[] {
    const keys: string[] = [];
    try {
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key) keys.push(key);
      }
    } catch (error) {
      console.error('Failed to get storage keys:', error);
    }
    return keys;
  }

  secureLogout(): void {
    this.clear();
  }

  isAuthenticated(): boolean {
    const token = this.getItem('access_token');
    const loginTime = this.getItem('login_time');
    
    if (!token || !loginTime) return false;
    
    // Check if token is expired (24 hours)
    const loginTimestamp = parseInt(loginTime);
    const currentTime = Date.now();
    const twentyFourHours = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
    
    return (currentTime - loginTimestamp) <= twentyFourHours;
  }

  getAuthInfo(): { token: string | null; email: string | null; loginTime: string | null } {
    return {
      token: this.getItem('access_token'),
      email: this.getItem('user_email'),
      loginTime: this.getItem('login_time')
    };
  }

  setAuthInfo(token: string, email: string): void {
    const loginTime = Date.now().toString();
    
    // Store in encrypted storage
    this.setItem('access_token', token);
    this.setItem('user_email', email);
    this.setItem('login_time', loginTime);
    
    // Also store in cookies for middleware access
    if (typeof document !== 'undefined') {
      document.cookie = `access_token=${token}; path=/; max-age=86400; SameSite=Strict`;
      document.cookie = `user_email=${email}; path=/; max-age=86400; SameSite=Strict`;
      document.cookie = `login_time=${loginTime}; path=/; max-age=86400; SameSite=Strict`;
    }
  }
}

// Secure storage manager
class SecureStorageManager {
  private memoryStorage: MemoryStorage;
  private encryptedStorage: EncryptedStorage;
  private sensitiveKeys: Set<string>;

  constructor() {
    this.memoryStorage = new MemoryStorage();
    this.encryptedStorage = new EncryptedStorage();
    this.sensitiveKeys = new Set([
      'admin-token',
      'two_factor_token',
      'session-token',
      'password',
      'platform_password',
      'hashed_password'
    ]);
  }

  // Store sensitive data in memory (cleared on page refresh)
  setSensitiveItem(key: string, value: string): void {
    this.memoryStorage.setItem(key, value);
  }

  // Get sensitive data from memory
  getSensitiveItem(key: string): string | null {
    return this.memoryStorage.getItem(key);
  }

  // Store non-sensitive data in encrypted localStorage
  setItem(key: string, value: string): void {
    if (this.sensitiveKeys.has(key)) {
      this.setSensitiveItem(key, value);
    } else {
      this.encryptedStorage.setItem(key, value);
    }
  }

  // Get data from appropriate storage
  getItem(key: string): string | null {
    if (this.sensitiveKeys.has(key)) {
      return this.getSensitiveItem(key);
    } else {
      return this.encryptedStorage.getItem(key);
    }
  }

  // Remove item from appropriate storage
  removeItem(key: string): void {
    if (this.sensitiveKeys.has(key)) {
      this.memoryStorage.removeItem(key);
    } else {
      this.encryptedStorage.removeItem(key);
    }
  }

  // Clear all storage
  clear(): void {
    this.memoryStorage.clear();
    this.encryptedStorage.clear();
  }

  // Check if item exists
  hasItem(key: string): boolean {
    return this.getItem(key) !== null;
  }

  // Get all keys (for debugging only)
  getAllKeys(): string[] {
    const keys: string[] = [];
    
    // Get memory storage keys
    // Note: MemoryStorage doesn't expose keys, so we can't enumerate them
    
    // Get localStorage keys (encrypted)
    try {
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key) keys.push(key);
      }
    } catch (error) {
      console.error('Failed to get storage keys:', error);
    }
    
    return keys;
  }

  // Secure logout - clear all sensitive data
  secureLogout(): void {
    this.memoryStorage.clear();
    this.encryptedStorage.clear();
    
    // Also clear cookies
    if (typeof document !== 'undefined') {
      document.cookie = 'access_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
      document.cookie = 'token_type=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
      document.cookie = 'user_email=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
      document.cookie = 'login_time=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
      document.cookie = 'admin-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
    }
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    const token = this.getItem('access_token');
    const loginTime = this.getItem('login_time');
    
    if (!token || !loginTime) return false;
    
    // Check if token is expired (24 hours)
    const loginTimestamp = parseInt(loginTime);
    const currentTime = Date.now();
    const twentyFourHours = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
    
    return (currentTime - loginTimestamp) <= twentyFourHours;
  }

  // Get authentication info
  getAuthInfo(): { token: string | null; email: string | null; loginTime: string | null } {
    return {
      token: this.getItem('access_token'),
      email: this.getItem('user_email'),
      loginTime: this.getItem('login_time')
    };
  }

  // Set authentication info
  setAuthInfo(token: string, email: string): void {
    const loginTime = Date.now().toString();
    
    // Store in secure storage
    this.setItem('access_token', token);
    this.setItem('user_email', email);
    this.setItem('login_time', loginTime);
    
    // Also store in cookies for middleware access
    if (typeof document !== 'undefined') {
      document.cookie = `access_token=${token}; path=/; max-age=86400; SameSite=Strict`;
      document.cookie = `user_email=${email}; path=/; max-age=86400; SameSite=Strict`;
      document.cookie = `login_time=${loginTime}; path=/; max-age=86400; SameSite=Strict`;
    }
  }
}

// Create singleton instance
const secureStorage = new SecureStorageManager();

// Export secure storage functions
export const secureStorageAPI = {
  // Sensitive data storage (memory only)
  setSensitiveItem: (key: string, value: string) => secureStorage.setSensitiveItem(key, value),
  getSensitiveItem: (key: string) => secureStorage.getSensitiveItem(key),
  
  // General storage (encrypted)
  setItem: (key: string, value: string) => secureStorage.setItem(key, value),
  getItem: (key: string) => secureStorage.getItem(key),
  removeItem: (key: string) => secureStorage.removeItem(key),
  clear: () => secureStorage.clear(),
  hasItem: (key: string) => secureStorage.hasItem(key),
  
  // Security functions
  secureLogout: () => secureStorage.secureLogout(),
  getAllKeys: () => secureStorage.getAllKeys(),

  // Auth helpers
  setAuthInfo: (token: string, email: string) => secureStorage.setAuthInfo(token, email),
  isAuthenticated: () => secureStorage.isAuthenticated(),
  getAuthInfo: () => secureStorage.getAuthInfo(),
};

// Migration helper to move existing localStorage data to secure storage
export const migrateToSecureStorage = () => {
  try {
    const keysToMigrate = [
      'access_token',
      'admin-token',
      'two_factor_token',
      'session-token',
      'verification_email',
      'reset_email',
      'selectedAccountId',
      'Name',
      'accountStatus',
      'platform_login',
      'server',
      'password',
      'session_id',
      'terminal_id',
      'profit_target',
      'orderFailed'
    ];

    keysToMigrate.forEach(key => {
      const value = localStorage.getItem(key);
      if (value) {
        secureStorage.setItem(key, value);
        // Remove from localStorage after migration
        localStorage.removeItem(key);
      }
    });

    console.log('Migration to secure storage completed');
  } catch (error) {
    console.error('Migration failed:', error);
  }
};

// Auto-migrate on module load
if (typeof window !== 'undefined') {
  migrateToSecureStorage();
}

export default secureStorageAPI; 