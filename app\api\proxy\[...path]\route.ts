import { NextRequest, NextResponse } from 'next/server';
import { getBaseUrl } from '@/app/config/env';
import { sanitizeErrorResponse } from '@/app/config/api';
import { 
  validateRequest, 
  RateLimiter, 
  logSecurityEvent,
  getSecurityHeaders,
  sanitizeSQLInput,
  InputValidator 
} from '@/lib/security';

// Fake base URL for display purposes
const FAKE_BASE_URL = 'https://api.fundedhorizon.com';

export async function GET(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return handleProxyRequest(request, params.path, 'GET');
}

export async function POST(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return handleProxyRequest(request, params.path, 'POST');
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return handleProxyRequest(request, params.path, 'PUT');
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return handleProxyRequest(request, params.path, 'DELETE');
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return handleProxyRequest(request, params.path, 'PATCH');
}

async function handleProxyRequest(
  request: NextRequest,
  pathSegments: string[],
  method: string
) {
  try {
    // Security: Validate request
    const requestValidation = validateRequest(request);
    if (!requestValidation.valid) {
      const fakePath = pathSegments.join('/');
      logSecurityEvent('Proxy request blocked', { reason: requestValidation.reason, path: fakePath }, 'high');
      return new NextResponse('Forbidden', { status: 403 });
    }

    // Security: Rate limiting
    const clientIP = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
    const rateLimit = RateLimiter.checkRateLimit(`proxy:${clientIP}`);
    
    if (!rateLimit.allowed) {
      const fakePath = pathSegments.join('/');
      logSecurityEvent('Proxy rate limit exceeded', { clientIP, path: fakePath }, 'medium');
      return new NextResponse('Too Many Requests', { 
        status: 429,
        headers: {
          'Retry-After': Math.ceil((rateLimit.resetTime - Date.now()) / 1000).toString()
        }
      });
    }

    // Security: Validate and sanitize path segments
    const sanitizedPathSegments = pathSegments.map(segment => {
      const sanitized = sanitizeSQLInput(segment);
      if (sanitized !== segment) {
        logSecurityEvent('Path injection attempt detected', { 
          original: segment, 
          sanitized, 
          clientIP 
        }, 'high');
      }
      return sanitized;
    });

    const realBackendUrl = getBaseUrl();
    const targetPath = sanitizedPathSegments.join('/');
    
    // Get query parameters from the original request
    const url = new URL(request.url);
    const queryParams = url.searchParams.toString();
    
    // Construct target URL with query parameters
    const targetUrl = queryParams 
      ? `${realBackendUrl}${targetPath}?${queryParams}`
      : `${realBackendUrl}${targetPath}`;
    const fakeTargetUrl = `${FAKE_BASE_URL}/${targetPath}`;

    // Security: Validate target URL
    if (!targetUrl.startsWith(realBackendUrl)) {
      logSecurityEvent('Invalid proxy target URL', { targetUrl: fakeTargetUrl, clientIP }, 'high');
      return new NextResponse('Forbidden', { status: 403 });
    }

    // Create new headers without problematic ones
    const headers = new Headers();
    request.headers.forEach((value, key) => {
      const lowerKey = key.toLowerCase();
      // Skip headers that can cause issues or security risks
      if (!['host', 'connection', 'content-length', 'x-forwarded-host', 'x-real-ip'].includes(lowerKey)) {
        // Security: Sanitize header values
        const sanitizedValue = InputValidator.sanitizeString(value);
        headers.set(key, sanitizedValue);
      }
    });

    // For non-GET requests, handle body properly
    let body: any = undefined;
    if (method !== 'GET') {
      const contentType = request.headers.get('content-type');
      
      // Special handling for verify-email endpoint with query parameters
      if (targetPath === 'auth/verify-email' && queryParams) {
        // For verify-email with query params, don't read body
        console.log('Verify-email request with query params:', {
          targetUrl,
          queryParams,
          method
        });
      } else if (contentType) {
        if (contentType.includes('multipart/form-data')) {
          // For FormData, we need to handle it specially
          const formData = await request.formData();
          
          // Security: Validate and sanitize form data
          const sanitizedFormData = new FormData();
          for (const [key, value] of formData.entries()) {
            if (typeof value === 'string') {
              const sanitizedKey = sanitizeSQLInput(key);
              const sanitizedValue = InputValidator.sanitizeString(value);
              sanitizedFormData.append(sanitizedKey, sanitizedValue);
            } else if (value instanceof File) {
              // Security: Validate file upload
              const fileValidation = InputValidator.validateFileUpload(value);
              if (!fileValidation.isValid) {
                logSecurityEvent('Invalid file upload attempt', { 
                  fileName: value.name, 
                  fileType: value.type, 
                  fileSize: value.size,
                  clientIP 
                }, 'medium');
                return NextResponse.json(
                  { error: fileValidation.error },
                  { status: 400 }
                );
              }
              sanitizedFormData.append(key, value);
            }
          }
          body = sanitizedFormData;
          // Remove content-type header for FormData to let browser set it
          headers.delete('content-type');
        } else if (contentType.includes('application/json')) {
          // For JSON, read and sanitize
          const jsonData = await request.json();
          const sanitizedData = InputValidator.sanitizeObject(jsonData);
          body = JSON.stringify(sanitizedData);
          headers.set('content-type', 'application/json');
        } else {
          // For other types, read as text and sanitize
          const textData = await request.text();
          const sanitizedText = InputValidator.sanitizeString(textData);
          body = sanitizedText;
          if (contentType) {
            headers.set('content-type', contentType);
          }
        }
      }
      // If no content-type, don't read body (for requests like POST with query params only)
    }

    // Make the request to backend (using real URL but logging fake URL)
    if (process.env.NODE_ENV !== 'production') {
      console.log('Proxy request:', {
        method,
        targetUrl: fakeTargetUrl,
        realTargetUrl: targetUrl,
        hasBody: !!body,
        contentType: headers.get('content-type'),
        queryParams
      });
    }
    
    const response = await fetch(targetUrl, {
      method,
      headers,
      body,
    });

    if (process.env.NODE_ENV !== 'production') {
      console.log('Proxy response:', {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries())
      });
    }

    // Get response data
    const responseText = await response.text();
    
    // Security: Sanitize response data if it's JSON and replace any real URLs with fake ones
    let sanitizedResponseText = responseText;
    try {
      const jsonData = JSON.parse(responseText);
      const sanitizedData = InputValidator.sanitizeObject(jsonData);
      sanitizedResponseText = JSON.stringify(sanitizedData);
    } catch {
      // Not JSON, sanitize as text
      sanitizedResponseText = InputValidator.sanitizeString(responseText);
    }
    
    // Additional sanitization to replace any real base URLs with fake ones
    sanitizedResponseText = sanitizeErrorResponse(sanitizedResponseText);
    
    // Create response
    const proxyResponse = new NextResponse(sanitizedResponseText, {
      status: response.status,
      statusText: response.statusText,
    });

    // Copy response headers (with sanitization)
    response.headers.forEach((value, key) => {
      const lowerKey = key.toLowerCase();
      if (!['content-encoding', 'transfer-encoding', 'content-length'].includes(lowerKey)) {
        const sanitizedValue = InputValidator.sanitizeString(value);
        proxyResponse.headers.set(key, sanitizedValue);
      }
    });

    // Add security headers
    const securityHeaders = getSecurityHeaders();
    Object.entries(securityHeaders).forEach(([key, value]) => {
      proxyResponse.headers.set(key, value);
    });

    // Add rate limit headers
    proxyResponse.headers.set('X-RateLimit-Limit', '100');
    proxyResponse.headers.set('X-RateLimit-Remaining', rateLimit.remainingAttempts.toString());
    proxyResponse.headers.set('X-RateLimit-Reset', rateLimit.resetTime.toString());

    logSecurityEvent('Proxy request successful', { 
      path: targetPath, 
      method, 
      status: response.status,
      clientIP 
    }, 'low');

    return proxyResponse;

  } catch (error) {
    logSecurityEvent('Proxy error', { 
      error: error instanceof Error ? error.message : 'Unknown error',
      path: pathSegments.join('/'),
      method 
    }, 'high');
    return NextResponse.json(
      { 
        error: 'Proxy request failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
} 