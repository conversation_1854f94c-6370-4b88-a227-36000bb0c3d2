import { NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { sanitizeErrorResponse } from '@/app/config/api';

export async function GET(
  request: Request,
  { params }: { params: { orderId: string } }
) {
  try {
    const headersList = headers();
    const authHeader = headersList.get('Authorization');

    if (!authHeader) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { orderId } = params;

    if (!orderId) {
      return NextResponse.json(
        { error: 'Order ID is required' },
        { status: 400 }
      );
    }

    console.log(`Fetching account details for order ID: ${orderId}`);

    // Use the proxy pattern instead of calling backend directly
    const response = await fetch(`/api/proxy/ordors/account_detail/${orderId}`, {
      method: 'GET',
      headers: {
        'Authorization': authHeader,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      const sanitizedErrorText = sanitizeErrorResponse(errorText);
      return NextResponse.json(
        { error: sanitizedErrorText },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log('Account details response:', data);

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in account details API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
