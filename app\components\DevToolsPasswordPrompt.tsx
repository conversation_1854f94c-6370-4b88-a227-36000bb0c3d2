'use client'
import { useEffect, useState } from 'react';

export default function DevToolsPasswordPrompt() {
  const [overlay, setOverlay] = useState(false);

  useEffect(() => {
    let devtoolsOpen = false;
    const password = 'Lunlaylo'; // Change this to your desired password
    let promptActive = false;

    const showOverlay = () => setOverlay(true);
    const hideOverlay = () => setOverlay(false);

    const checkDevTools = () => {
      // Skip detection on mobile devices
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      if (isMobile) {
        hideOverlay();
        return;
      }

      // Method 1: Size difference detection (less aggressive)
      const threshold = 160;
      const widthThreshold = window.outerWidth - window.innerWidth > threshold;
      const heightThreshold = window.outerHeight - window.innerHeight > threshold;
      
      // Method 2: Screen size checks
      const isSmallScreen = window.innerWidth < 768 || window.innerHeight < 600;
      const isMaximized = window.outerWidth === window.screen.availWidth && window.outerHeight === window.screen.availHeight;
      
      // Combined detection logic (simplified)
      const devToolsDetected = (
        widthThreshold || 
        heightThreshold
      ) && 
      !isSmallScreen && 
      !isMaximized && 
      !promptActive;
      
      if (devToolsDetected) {
        devtoolsOpen = true;
        promptActive = true;
        showOverlay();
        
        const askPassword = () => {
          const userInput = prompt('🔒 Enter password to access DevTools:');
          if (userInput === null) {
            // User clicked cancel, keep asking
            setTimeout(askPassword, 100);
          } else if (userInput !== password) {
            alert('❌ Incorrect password. DevTools access denied.');
            setTimeout(askPassword, 100);
          } else {
            promptActive = false;
            hideOverlay();
            alert('✅ Access granted!');
          }
        };
        askPassword();
      } else if (!(widthThreshold || heightThreshold)) {
        devtoolsOpen = false;
        promptActive = false;
        hideOverlay();
      }
    };

    // Check after a delay to allow page to load first
    const initialCheck = setTimeout(checkDevTools, 2000);
    
    // Set up interval with longer delay
    const interval = setInterval(checkDevTools, 2000);
    
    // Additional event listeners
    const handleResize = () => {
      setTimeout(checkDevTools, 500);
    };
    
    const handleKeyDown = (e: KeyboardEvent) => {
      // Detect F12 key
      if (e.key === 'F12') {
        setTimeout(checkDevTools, 500);
      }
      // Detect Ctrl+Shift+I (Chrome DevTools)
      if (e.ctrlKey && e.shiftKey && e.key === 'I') {
        setTimeout(checkDevTools, 500);
      }
      // Detect Ctrl+Shift+J (Chrome Console)
      if (e.ctrlKey && e.shiftKey && e.key === 'J') {
        setTimeout(checkDevTools, 500);
      }
      // Detect Ctrl+U (View Source)
      if (e.ctrlKey && e.key === 'u') {
        setTimeout(checkDevTools, 500);
      }
      // Detect Ctrl+Shift+C (Chrome Elements)
      if (e.ctrlKey && e.shiftKey && e.key === 'C') {
        setTimeout(checkDevTools, 500);
      }
    };

    // Prevent right-click context menu
    const handleContextMenu = (e: MouseEvent) => {
      e.preventDefault();
      setTimeout(checkDevTools, 500);
    };

    window.addEventListener('resize', handleResize);
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('contextmenu', handleContextMenu);

    return () => {
      clearTimeout(initialCheck);
      clearInterval(interval);
      window.removeEventListener('resize', handleResize);
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('contextmenu', handleContextMenu);
    };
  }, []);

  return overlay ? (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      width: '100vw',
      height: '100vh',
      background: 'rgba(0,0,0,0.95)',
      zIndex: 99999,
      pointerEvents: 'all',
      cursor: 'not-allowed',
      userSelect: 'none',
      overscrollBehavior: 'none',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      color: 'white',
      fontSize: '20px',
      fontWeight: 'bold',
      textAlign: 'center',
    }}>
      <div>
        🔒 DevTools Access Restricted<br/>
        <span style={{ fontSize: '14px', opacity: 0.8 }}>
          Enter password to continue
        </span>
      </div>
    </div>
  ) : null;
} 