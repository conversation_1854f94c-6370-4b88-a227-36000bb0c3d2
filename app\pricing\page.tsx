"use client"

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { T<PERSON>dingUp, Shield, Clock, ArrowRight, Zap, Sparkles, Crown, Star, Check } from 'lucide-react'
import { Navbar } from '@/app/component/navbar'
import { Footer } from '@/app/component/components_footer'
import { SaleOfferModal } from '@/app/components/sale-offer-modal'

const ACCOUNT_SIZES = [
  "$1,000", "$3,000", "$5,000", "$10,000", "$25,000",
  "$50,000", "$100,000", "$200,000", "$500,000"
] as const

type AccountSize = typeof ACCOUNT_SIZES[number]

const CHALLENGE_TYPES = {
  'HFT NEO': {
    color: "from-purple-600 to-purple-800",
    icon: Crown,
    description: "For elite traders seeking maximum performance",
    rules: {
      "Profit Target": "8%",
      "Daily Drawdown": "5%",
      "Max Drawdown": "10%",
      "Profit Split": "Up to 90%",
      "Min Trading Days": "0",
      "Leverage": "1:100"
    },
    prices: {
      "$1,000": "$18.75",
      "$3,000": "$33.75",
      "$5,000": "$46.25",
      "$10,000": "$78.75",
      "$25,000": "$172.50",
      "$50,000": "$316.67",
      "$100,000": "$550.00",
      "$200,000": "$896.67",
      "$500,000": "$1,616.67"
    },
    salePrice: {
      "$1,000": "$15",
      "$3,000": "$27",
      "$5,000": "$37",
      "$10,000": "$63",
      "$25,000": "$138",
      "$50,000": "$95",
      "$100,000": "$165",
      "$200,000": "$269",
      "$500,000": "$485"
    },
    features: [
      "Ultra-fast execution speeds",
      "Advanced trading tools access",
      "Priority support 24/7",
      "Custom leverage options",
      "Real-time performance analytics"
    ],
    popularityScore: 98,
    badge: "Elite Choice",
    savings: {
      "$1,000": "Save $3.75",
      "$3,000": "Save $6.75",
      "$5,000": "Save $9.25",
      "$10,000": "Save $15.75",
      "$25,000": "Save $34.50",
      "$50,000": "Save $221.67",
      "$100,000": "Save $385.00",
      "$200,000": "Save $627.67",
      "$500,000": "Save $1,131.67"
    }
  },
  'One-Step': {
    color: "from-[#B4A46A] to-[#8A7B4D]",
    icon: Zap,
    description: "Advanced challenge for consistent traders",
    rules: {
      "Profit Target": "10%",
      "Daily Drawdown": "4%",
      "Max Drawdown": "10%",
      "Profit Split": "Up to 80%",
      "Min Trading Days": "5",
      "Leverage": "1:100"
    },
    prices: {
      "$1,000": "$10.00",
      "$3,000": "$18.75",
      "$5,000": "$30.00",
      "$10,000": "$52.50",
      "$25,000": "$107.50",
      "$50,000": "$183.33",
      "$100,000": "$296.67",
      "$200,000": "$496.67",
      "$500,000": "$963.33"
    },
    salePrice: {
      "$1,000": "$8",
      "$3,000": "$15",
      "$5,000": "$24",
      "$10,000": "$42",
      "$25,000": "$86",
      "$50,000": "$55",
      "$100,000": "$89",
      "$200,000": "$149",
      "$500,000": "$289"
    },
    features: [
      "Intermediate trading conditions",
      "Standard support package",
      "Basic performance metrics",
      "Regular market updates",
      "Trading community access"
    ],
    popularityScore: 92,
    badge: "Most Popular",
    savings: {
      "$1,000": "Save $2.00",
      "$3,000": "Save $3.75",
      "$5,000": "Save $6.00",
      "$10,000": "Save $10.50",
      "$25,000": "Save $21.50",
      "$50,000": "Save $128.33",
      "$100,000": "Save $207.67",
      "$200,000": "Save $347.67",
      "$500,000": "Save $674.33"
    }
  },
  'Two-Step': {
    color: "from-[#0A1A2F] to-[#162A46]",
    icon: Shield,
    description: "Perfect starting point for new traders",
    rules: {
      "Profit Target": "10%",
      "Profit Second target": "5%",
      "Daily Drawdown": "4%",
      "Max Drawdown": "10%",
      "Profit Split": "Up to 80%",
      "Min Trading Days": "5",
      "Leverage": "1:100"
    },
    prices: {
      "$1,000": "$7.50",
      "$3,000": "$15.00",
      "$5,000": "$26.25",
      "$10,000": "$41.25",
      "$25,000": "$90.00",
      "$50,000": "$140.00",
      "$100,000": "$216.67",
      "$200,000": "$426.67",
      "$500,000": "$750.00"
    },
    salePrice: {
      "$1,000": "$6",
      "$3,000": "$12",
      "$5,000": "$21",
      "$10,000": "$33",
      "$25,000": "$72",
      "$50,000": "$42",
      "$100,000": "$65",
      "$200,000": "$128",
      "$500,000": "$225"
    },
    features: [
      "Beginner-friendly rules",
      "Educational resources",
      "Basic support package",
      "Standard market tools",
      "Weekly market insights"
    ],
    popularityScore: 85,
    badge: "Best Value",
    savings: {
      "$1,000": "Save $1.50",
      "$3,000": "Save $3.00",
      "$5,000": "Save $5.25",
      "$10,000": "Save $8.25",
      "$25,000": "Save $18.00",
      "$50,000": "Save $98.00",
      "$100,000": "Save $151.67",
      "$200,000": "Save $298.67",
      "$500,000": "Save $525.00"
    }
  }
}

function PricingContent() {
  const [selectedBalance, setSelectedBalance] = useState<AccountSize>("$10,000")

  return (
    <main className="w-full">
      {/* Hero Section */}
      <section className="relative min-h-[40vh] flex items-center justify-center pt-24 pb-12">
        <div className="absolute inset-0 bg-[url('/grid.svg')] opacity-10" />
        <div className="absolute inset-0 bg-gradient-to-b from-[#B4A46A]/20 to-transparent" />
        <div className="relative text-center max-w-5xl mx-auto px-4">
          <motion.h1 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-[#E5D5A3] to-[#B4A46A] bg-clip-text text-transparent mb-6"
          >
            Premium Trading Challenges
          </motion.h1>
          <motion.p 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="text-lg md:text-xl text-[#B4A46A]/80 max-w-3xl mx-auto"
          >
            Choose your ideal trading challenge and start your journey to becoming a funded trader
          </motion.p>
        </div>
      </section>

      {/* Account Size Selector */}
      <section className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-[#B4A46A] mb-4">Select Account Size</h2>
        </div>
        <div className="flex flex-wrap justify-center gap-3 max-w-4xl mx-auto">
          {ACCOUNT_SIZES.map((size) => (
            <button
              key={size}
              onClick={() => setSelectedBalance(size)}
              className={`px-6 py-3 rounded-xl transition-all duration-300 ${
                selectedBalance === size
                  ? "bg-gradient-to-r from-[#B4A46A] to-[#E5D5A3] text-[#0A1A2F] font-bold shadow-[0_4px_20px_rgba(180,164,106,0.3)]"
                  : "bg-[#162A46] text-[#B4A46A]/60 hover:text-[#B4A46A] hover:bg-[#1C3356]"
              }`}
            >
              {size}
            </button>
          ))}
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="container mx-auto px-4 py-8">
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {Object.entries(CHALLENGE_TYPES).map(([name, data]) => (
            <motion.div
              key={name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="relative bg-gradient-to-br from-[#0A1A2F] to-[#162A46] rounded-2xl overflow-hidden group"
            >
              {/* Badge */}
              {data.badge && (
                <div className="absolute top-4 right-4 px-4 py-1 bg-gradient-to-r from-[#B4A46A] to-[#E5D5A3] text-[#0A1A2F] text-xs font-bold rounded-full shadow-[0_4px_15px_rgba(180,164,106,0.3)] flex items-center gap-1.5">
                  <Crown className="w-3.5 h-3.5" />
                  <span>{data.badge}</span>
                </div>
              )}

              {/* Card Content */}
              <div className="p-8">
                <div className="flex items-center gap-4 mb-6">
                  <div className={`p-3 rounded-xl bg-gradient-to-r ${data.color}`}>
                    <data.icon className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-white mb-1">{name}</h3>
                    <p className="text-sm text-[#B4A46A]/80">{data.description}</p>
                  </div>
                </div>

                {/* Price */}
                <div className="mb-8">
                  <div className="flex items-baseline gap-2">
                    <span className="text-3xl font-bold text-[#E5D5A3]">
                      {data.salePrice[selectedBalance]}
                    </span>
                    <span className="text-lg text-[#B4A46A]/60 line-through">
                      {data.prices[selectedBalance]}
                    </span>
                  </div>
                  <p className="text-sm text-[#B4A46A] mt-1">{data.savings[selectedBalance]}</p>
                </div>

                {/* Features */}
                <div className="space-y-4 mb-8">
                  {Object.entries(data.rules).map(([rule, value]) => (
                    <div key={rule} className="flex items-center justify-between text-sm">
                      <span className="text-[#B4A46A]/80">{rule}</span>
                      <span className="text-white font-medium">{value}</span>
                    </div>
                  ))}
                </div>

                {/* Additional Features */}
                <div className="space-y-3 mb-8">
                  {data.features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm">
                      <Check className="w-4 h-4 text-[#B4A46A]" />
                      <span className="text-[#B4A46A]/80">{feature}</span>
                    </div>
                  ))}
                </div>

                {/* CTA Button */}
                <button
                  onClick={() => window.location.href = '/signup'}
                  className={`w-full bg-gradient-to-r ${data.color} text-white py-4 rounded-xl font-bold text-sm flex items-center justify-center gap-2 transition-all duration-300 hover:brightness-110 hover:scale-[1.02] shadow-[0_4px_20px_rgba(180,164,106,0.2)]`}
                >
                  Get Started Now
                  <ArrowRight className="w-4 h-4" />
                </button>
              </div>
            </motion.div>
          ))}
        </div>
      </section>
    </main>
  )
}

export default function PricingPage() {
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  if (!isMounted) {
    return null
  }

  return (
    <div className="relative min-h-screen flex flex-col bg-[#0A0F1C]">
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-[url('/grid.svg')] opacity-10" />
        <div className="absolute inset-0 bg-gradient-to-b from-[#B4A46A]/10 to-transparent" />
      </div>
      
      <div className="relative z-10 flex flex-col min-h-screen">
        <Navbar />
        <div className="flex-1 flex flex-col items-center justify-start">
          <PricingContent />
        </div>
        <Footer />
      </div>
      
      <SaleOfferModal />
    </div>
  )
} 